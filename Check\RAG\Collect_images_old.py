

# async def collect_all_trademark_images(folder_path, case_type):
#     # How to use:
#     # 1. Run collect_all_trademark_images() to download the trademarks
#     # 2. Remove text from logo folder (5 min)
#     # 3. Identify logo in the text folder and move to logo folder (15 Minutes for 6,000 files)
#     # 4. Remove duplicates in logo folder : remove_duplicates('D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Trademarks/Logo - NoDupe/')
#     # 5. Build descriptors dataset : build_descriptors_dataset()
    
#     df = get_table_from_GZ("tb_case")
#     if not os.path.exists(folder_path):
#         os.makedirs(folder_path)
#     tasks = []
#     semaphore = asyncio.Semaphore(20)

#     # df_cases = df_cases.head(20)
#     for index, row in df.iterrows():
#         plaintiff_id = row["plaintiff_id"]
#         if case_type in row["images"]:
#             for image in row["images"][case_type].keys():
#                 image_data = row["images"][case_type][image]
#                 category = "Text" if image_data["trademark_text"][0] != "" else "Logo"
#                 url = f"https://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image}"
            
#                 file_local_name = row['update_time'].date().strftime("%Y-%m-%d") + "_" + str(plaintiff_id) + "_" + image

#                 file_local_name = sanitize_name(file_local_name)
#                 file_local_path = os.path.join(os.getcwd(), "Documents", "AssessFiles", "For AI", "Trademarks",category, sanitize_name(file_local_name))

#                 error_file = os.path.join(os.getcwd(), "Documents", "AssessFiles", "For AI", "Trademarks", "Errors", "IP_Download_Error.txt")
#                 error_message = f"Plaintiff ID:{plaintiff_id} - Case: {row['docket']} - Files on: {row['date_filed']} - Image: {image_data} - Link: {url}\n"

#                 tasks.append(sem_task(semaphore, download_from_url(url, file_local_path, error_file, error_message)))  

#     # Process all downloads concurrently
#     await asyncio.gather(*tasks)


# async def collect_all_copyright_images(folder_path, case_type, existing_folder_path=None):
#     df = get_table_from_GZ("tb_case")
#     if not os.path.exists(folder_path):
#         os.makedirs(folder_path)
#     tasks = []
#     semaphore = asyncio.Semaphore(20)
#     for index, row in df.iterrows():
#         if case_type in row['images']:
#             for image in row['images'][case_type].keys():
#                 target_path = f"{folder_path}/{image}"
                
#                 # Avoid redownloading pictures which we have already downloaded
#                 if os.path.exists(target_path):
#                     continue
#                 if existing_folder_path: 
#                     existing_path = f"{existing_folder_path}/{image}"
#                     if os.path.exists(existing_path):
#                         continue

#                 url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{row['plaintiff_id']}/high/{image}"
#                 tasks.append(sem_task(semaphore, download_from_url(url, target_path)))  

#     # Process all downloads concurrently
#     if tasks:
#         await asyncio.gather(*tasks)
#     else:
#         print("No new images to download.")


# async def collect_all_patent_images(folder_path, case_type):
#     df = get_table_from_GZ("tb_case")

#     if not os.path.exists(folder_path):
#         os.makedirs(folder_path)
#     tasks = []
#     semaphore = asyncio.Semaphore(20)
#     for index, row in df.iterrows():
#         if case_type in row['images']:
#             for i, image in enumerate(row['images'][case_type].keys()):
#                 url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{row['plaintiff_id']}/high/{image}"
                
#                 if "product_name" in row['images'][case_type][image] and row['images'][case_type][image]['product_name'] != "":
#                     file_name = f"{row['images'][case_type][image]['product_name']}_{image}"
#                 else:
#                     file_name = image

#                 tasks.append(sem_task(semaphore, download_from_url(url, f"{folder_path}/{file_name}")))  

#     # Process all downloads concurrently
#     await asyncio.gather(*tasks)