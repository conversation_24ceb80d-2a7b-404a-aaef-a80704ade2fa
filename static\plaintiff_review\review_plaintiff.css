/* Specific button groupings if needed, otherwise rely on .actions-row and .button-group */
.left-buttons {
    display: flex; /* Keep if specific grouping is needed beyond .button-group */
    gap: var(--spacing-md); /* Use common variable */
}
.right-buttons {
    display: flex; /* Keep if specific grouping is needed beyond .button-group */
    align-items: center;
    gap: var(--spacing-md); /* Use common variable */
}

/* Overlay styles are handled by common.css .overlay */

/* Specific overlay content styling */
.overlay-list { /* Keep if specific list styling is needed */
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: var(--spacing-md); /* Use common variable */
    /* Add any other specific list styles here */
}

.plaintiff-names-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.plaintiff-names-cell:hover {
    overflow: visible;
    white-space: normal;
    background-color: var(--card-background);
    position: relative;
    z-index: 20;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    padding: 5px;
}

.action-cell {
    display: flex;
    gap: var(--spacing-md); /* Use var */
    align-items: center;
}

/* Use common .radio-option */
.approve-option label {
    color: var(--success-color); /* Use var */
}

.reject-option label {
    color: var(--error-color); /* Use var */
}
