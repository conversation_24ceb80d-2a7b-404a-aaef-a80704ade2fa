# IP/Trademarks/trademark_parser.py

from lxml import etree as ET
import json
from datetime import datetime
import logging
# Removed asyncio, aiohttp imports
from typing import List, Dict, Any, Tuple # Added Tuple
from tqdm import tqdm # Added for progress bar

# Removed trademark_image import

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trademark_parser.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def parse_daily_trademark_xml(
    xml_file_path: str
) -> Tuple[List[Dict[str, Any]], List[str]]: # Return trademarks and serials to download
    """
    Parse USPTO Daily/Bulk trademark XML file iteratively, extract structured data,
    and collect serial numbers for images that need downloading.

    Args:
        xml_file_path (str): Path to the XML file.

    Returns:
        tuple: (List of trademark dictionaries, List of serial numbers to download images for)
    """
    trademarks: List[Dict[str, Any]] = []
    serials_to_download: List[str] = [] # List to collect serial numbers
    context = None # Initialize context for potential error handling
    root = None # Initialize root for potential error handling
    pbar = None # Initialize progress bar variable

    try:
        # --- First Pass: Count total 'case-file' elements for progress bar ---
        logger.info(f"Counting case-file elements in {xml_file_path}...")
        total_case_files = 0
        count_context = ET.iterparse(xml_file_path, events=('end',))
        for _, elem in count_context:
            if elem.tag == 'case-file':
                total_case_files += 1
            # Clear element to save memory during counting pass
            elem.clear()
        # Clear the reference to the iterator
        del count_context
        logger.info(f"Found {total_case_files} case-file elements.")

        if total_case_files == 0:
             logger.warning("No case-file elements found in the XML. Exiting parsing.")
             return [], []

        # --- Second Pass: Parse with progress bar ---
        logger.info("Starting main parsing pass...")
        context = ET.iterparse(xml_file_path, events=('start', 'end'))
        # Get the root element to help with clearing memory later
        event, root = next(context) # Get root element for clearing
        pbar = tqdm(total=total_case_files, desc="Parsing Trademarks", unit=" case")

        # Process elements iteratively
        # Process elements iteratively
        for event, elem in context:
            # Process when a 'case-file' element ends
            if event == 'end' and elem.tag == 'case-file':
                # --- Start of processing a single case-file ---
                trademark: Dict[str, Any] = {}
                try: # Removed download_task initialization
                    # Basic fields
                    trademark['reg_no'] = get_element_text(elem, './/registration-number')
                    trademark['ser_no'] = get_element_text(elem, './/serial-number')
                    trademark['TRO'] = None
                    trademark['image_source'] = None # Default image source

                    # Get case-file-header
                    case_file_header = elem.find('.//case-file-header')
                    if case_file_header is not None:
                        trademark['mark_text'] = get_element_text(case_file_header, './/mark-identification')
                        filing_date_str = get_element_text(case_file_header, './/filing-date')
                        if filing_date_str:
                            try:
                                trademark['filing_date'] = datetime.strptime(filing_date_str, '%Y%m%d').date().isoformat()
                            except ValueError:
                                trademark['filing_date'] = None
                                logger.warning(f"Invalid filing date format for serial number {trademark.get('ser_no', 'N/A')}: {filing_date_str}")

                        mark_feature_code_str = get_element_text(case_file_header, './/mark-drawing-code')
                        mark_feature_code = None
                        if mark_feature_code_str:
                            try:
                                mark_feature_code = int(mark_feature_code_str)
                                trademark['mark_feature_code'] = mark_feature_code
                            except ValueError:
                                trademark['mark_feature_code'] = None
                                logger.warning(f"Invalid mark feature code for serial number {trademark.get('ser_no', 'N/A')}: {mark_feature_code_str}")
                        else:
                             trademark['mark_feature_code'] = None # Ensure it exists even if code is missing

                        mark_status_code_str = get_element_text(case_file_header, './/status-code')
                        if mark_status_code_str:
                            try:
                                trademark['mark_current_status_code'] = int(mark_status_code_str)
                            except ValueError:
                                trademark['mark_current_status_code'] = None
                                logger.warning(f"Invalid mark status code for serial number {trademark.get('ser_no', 'N/A')}: {mark_status_code_str}")

                        std_char_indicator = get_element_text(case_file_header, './/standard-characters-claimed-in')
                        if std_char_indicator:
                            trademark['mark_standard_character_indicator'] = std_char_indicator == 'T'

                    # --- Image Download Check ---
                    serial_number = trademark.get('ser_no')
                    # Check conditions for download
                    if mark_feature_code in [2, 3, 5] and serial_number:
                        logger.debug(f"Identified serial number for potential download: {serial_number}")
                        if serial_number not in serials_to_download: # Avoid duplicates
                             serials_to_download.append(serial_number)
                    # --- End Image Download Check ---
                    # Find owner with highest entry number
                    highest_entry_num = -1
                    highest_owner = None
                    for owner in elem.findall('.//case-file-owner'):
                        entry_num_str = get_element_text(owner, './/entry-number')
                        if entry_num_str:
                            try:
                                entry_num = int(entry_num_str)
                                if entry_num > highest_entry_num:
                                    highest_entry_num = entry_num
                                    highest_owner = owner
                            except ValueError:
                                pass # Ignore invalid entry numbers

                    if highest_owner is not None:
                        trademark['applicant_name'] = get_element_text(highest_owner, './/party-name')
                        country_codes = []
                        for nationality in highest_owner.findall('.//nationality'):
                            country = get_element_text(nationality, './/country')
                            if country and country not in country_codes:
                                country_codes.append(country)
                        trademark['country_codes'] = country_codes if country_codes else None

                    # Extract international classes
                    int_cls = []
                    for classification in elem.findall('.//classifications/classification'):
                        int_code = get_element_text(classification, './/international-code')
                        if int_code:
                            try:
                                if int_code != "A" and int_code != "B" and int_code not in int_cls: # Avoid duplicates
                                    int_cls.append(int(int_code))
                            except ValueError:
                                logger.warning(f"Invalid international code for serial number {trademark.get('ser_no', 'N/A')}: {int_code}")
                    trademark['int_cls'] = int_cls if int_cls else None

                    # Count number of suits
                    nb_suits = 0
                    for event_statement in elem.findall('.//case-file-event-statements/case-file-event-statement'):
                        code = get_element_text(event_statement, './/code')
                        if code == 'NOSUI':
                            nb_suits += 1
                    trademark['nb_suits'] = nb_suits

                    # Process goods and services
                    goods_services = []
                    for classification in elem.findall('.//classifications/classification'):
                        gs_item = {}
                        int_code = get_element_text(classification, './/international-code')
                        if int_code:
                            try:
                                gs_item['NiceClass'] = int(int_code)
                            except ValueError:
                                gs_item['NiceClass'] = None
                        gs_item['StatusCode'] = get_element_text(classification, './/status-code')
                        gs_item['FirstDateUsed'] = get_element_text(classification, './/first-use-anywhere-date')
                        gs_item['FirstUsedInCommerceDate'] = get_element_text(classification, './/first-use-in-commerce-date')
                        gs_item['subclass'] = None # Not available in this format
                        if gs_item.get('NiceClass') or gs_item.get('StatusCode'): # Only add if it has some data
                            goods_services.append(gs_item)
                    trademark['goods_services'] = json.dumps(goods_services) if goods_services else None

                    # Process case file statements
                    mark_disclaimer_text_daily = []
                    mark_image_colour_statement_daily = []
                    case_file_statements_other = []
                    goods_services_text_parts = [] # Collect GS text parts

                    for statement in elem.findall('.//case-file-statements/case-file-statement'):
                        type_code = get_element_text(statement, './/type-code')
                        text = get_element_text(statement, './/text')
                        if type_code and text:
                            if type_code in ['D0', 'D1']: mark_disclaimer_text_daily.append(text)
                            elif type_code in ['CC', 'CD']: mark_image_colour_statement_daily.append(text)
                            elif type_code == 'TR': trademark['mark_translation_statement_daily'] = text
                            elif type_code == 'N0': trademark['name_portrait_statement_daily'] = text
                            elif type_code == 'DM': trademark['mark_description_statement_daily'] = text
                            elif type_code == 'CS': trademark['certification_mark_statement_daily'] = text
                            elif type_code == 'LS': trademark['lining_stippling_statement_daily'] = text
                            elif type_code == 'TF': trademark['section_2f_statement_daily'] = text
                            elif type_code.startswith('GS'): goods_services_text_parts.append(text) # Collect GS parts
                            else: case_file_statements_other.append({'type_code': type_code, 'text': text})

                    trademark['mark_disclaimer_text_daily'] = mark_disclaimer_text_daily if mark_disclaimer_text_daily else None
                    trademark['mark_image_colour_statement_daily'] = mark_image_colour_statement_daily if mark_image_colour_statement_daily else None
                    trademark['case_file_statements_other'] = json.dumps(case_file_statements_other) if case_file_statements_other else None
                    trademark['goods_services_text_daily'] = ' '.join(goods_services_text_parts) if goods_services_text_parts else None # Join GS parts

                    # Find latest event description
                    latest_date = None
                    latest_event = None
                    highest_number = -1
                    for event in elem.findall('.//case-file-event-statements/case-file-event-statement'):
                        date_str = get_element_text(event, './/date')
                        number_str = get_element_text(event, './/number')
                        if date_str:
                            try:
                                event_date = datetime.strptime(date_str, '%Y%m%d').date()
                                event_number = int(number_str) if number_str else 0
                                if latest_date is None or event_date > latest_date or (event_date == latest_date and event_number > highest_number):
                                    latest_date = event_date
                                    highest_number = event_number
                                    latest_event = event
                            except ValueError:
                                pass # Ignore invalid dates/numbers
                    if latest_event is not None:
                        trademark['latest_event_description_daily'] = get_element_text(latest_event, './/description-text')

                    # Set fields not present in Daily/Bulk format to None
                    trademark['associated_marks'] = None
                    trademark['mark_disclaimer_text'] = None
                    trademark['mark_image_colour_claimed_text'] = None
                    trademark['mark_image_colour_part_claimed_text'] = None
                    trademark['national_design_code'] = None
                    trademark['mark_current_status_external_description_text'] = None

                    # Set metadata fields
                    trademark['last_updated_source'] = 'daily_api'

                    # Add trademark data to the list
                    trademarks.append(trademark)

                # --- End of processing a single case-file ---

                # --- Exception handling for a single case-file ---
                except Exception as e:
                    ser_no = get_element_text(elem, './/serial-number') # Try to get serial number for logging
                    logger.error(f"Error processing case file element with serial number {ser_no or 'UNKNOWN'}: {str(e)}", exc_info=True) # Add stack trace
                    # Still add the partially parsed item so we don't lose data
                    trademarks.append(trademark) # Add partially parsed trademark
                # --- End of exception handling ---

                # --- Finally block for a single case-file ---
                finally:
                    # Update progress bar *before* clearing
                    if pbar:
                        pbar.update(1)
                    # Crucial: Clear the element and its descendants to free memory
                    elem.clear()
                    # Attempt to clear parent references to free memory sooner (optional, usually not needed with elem.clear())
                    # while root and root.getprevious() is not None:
                    #     del root.getparent()[0]

        # --- End of main loop ---

        logger.info(f"Finished parsing XML. Found {len(trademarks)} trademarks and {len(serials_to_download)} serial numbers for potential image download.")
        return trademarks, serials_to_download

    # --- Exception handling for the entire parsing process ---
    except ET.ParseError as e:
        logger.error(f"Fatal XML ParseError in file {xml_file_path}: {str(e)}", exc_info=True)
        return [], [] # Return empty lists on fatal parse error
    except Exception as e:
        logger.error(f"Unexpected fatal error processing XML file {xml_file_path}: {str(e)}", exc_info=True)
        return [], [] # Return empty lists on other fatal errors
    finally:
        # Ensure progress bar is closed
        if pbar:
            pbar.close()
            logger.info("Progress bar closed.") # Add log message for clarity
        # Clear root reference to potentially help GC
        if root is not None: # Check if root was assigned
             root.clear() # Clear the root element as well
             root = None
        context = None # Clear context reference
        logger.info("Parser cleanup finished.")


def get_element_text(element, xpath):
    """
    Helper function to safely extract text from an XML element.

    Args:
        element (Element): XML element to search within
        xpath (str): XPath to the target element

    Returns:
        str or None: Text content of the element, or None if not found
    """
    found = element.find(xpath)
    return found.text.strip() if found is not None and found.text else None # Added strip()
