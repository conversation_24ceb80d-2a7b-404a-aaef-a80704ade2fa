"""
Similarity service for filtering and processing similarity results.
"""

from typing import List, Dict, Any

from .normalization_service import (
    normalize_copyright_clip_score,
    normalize_copyright_efficientnet_score,
    calculate_copyright_combined_score,
    normalize_and_combine_copyright_results
)

def apply_copyright_similarity_filtering(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Apply similarity filtering logic for copyright results.

    Args:
        results: A list of copyright similarity results.

    Returns:
        A filtered list of copyright similarity results.
    """
    filtered_results = []

    # Group results by query image path to process each query separately
    query_groups = {}
    for result in results:
        query_path = result.get("query_image_path", "")
        if query_path not in query_groups:
            query_groups[query_path] = []
        query_groups[query_path].append(result)

    # Process each query group separately
    for query_path, query_results in query_groups.items():
        # Group by approach (b7, clipv2, combined)
        approach_results = {
            "b7": [],
            "clipv2": [],
            "combined": []
        }

        for result in query_results:
            approach = result.get("approach", "combined")
            if approach in approach_results:
                approach_results[approach].append(result)

        # Process each approach
        for approach, approach_query_results in approach_results.items():
            if not approach_query_results:
                continue

            # Sort by similarity score
            approach_query_results.sort(key=lambda x: float(x.get("similarity", 0)), reverse=True)

            # Get top1 score and normalize it based on approach
            top1_score = float(approach_query_results[0].get("similarity", 0))
            normalized_top1_score = 0.0

            if approach == "b7":
                normalized_top1_score = normalize_copyright_efficientnet_score(top1_score)
            elif approach == "clipv2":
                normalized_top1_score = normalize_copyright_clip_score(top1_score)
            else:  # combined
                normalized_top1_score = top1_score  # Already normalized

            # Apply the same logic as in RAG_Copyright.py
            eligible_results = []
            seen_ids = set()

            # Top1 check (normalized score >= 1)
            if normalized_top1_score >= 1.0:
                eligible_results.append(approach_query_results[0])
                seen_ids.add(approach_query_results[0].get("ip_asset_id", ""))
                proximity_threshold = top1_score - 0.02  # Use raw score for proximity

                # Proximity check (within 0.02 of top1)
                for result in approach_query_results[1:]:
                    result_id = result.get("ip_asset_id", "")
                    if result_id in seen_ids:
                        continue

                    score = float(result.get("similarity", 0))
                    if score >= proximity_threshold:
                        eligible_results.append(result)
                        seen_ids.add(result_id)
                    else:
                        break

            # Top2/3 check (normalized score >= 1.13)
            for i in [1, 2]:
                if i < len(approach_query_results):
                    result = approach_query_results[i]
                    result_id = result.get("ip_asset_id", "")
                    score = float(result.get("similarity", 0))

                    # Normalize score based on approach
                    normalized_score = 0.0
                    if approach == "b7":
                        normalized_score = normalize_copyright_efficientnet_score(score)
                    elif approach == "clipv2":
                        normalized_score = normalize_copyright_clip_score(score)
                    else:  # combined
                        normalized_score = score  # Already normalized

                    if normalized_score >= 1.13 and result_id not in seen_ids:
                        eligible_results.append(result)
                        seen_ids.add(result_id)

            # Add eligible results to filtered results
            filtered_results.extend(eligible_results)

    # Remove duplicates based on ip_asset_id
    unique_results = {}
    for result in filtered_results:
        result_id = result.get("ip_asset_id", "")
        if result_id not in unique_results or float(result.get("similarity", 0)) > float(unique_results[result_id].get("similarity", 0)):
            unique_results[result_id] = result

    return list(unique_results.values())

from .normalization_service import normalize_patent_image_score, normalize_patent_text_score

def apply_patent_similarity_filtering(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Apply similarity filtering logic for patent results.

    Args:
        results: A list of patent similarity results.

    Returns:
        A filtered list of patent similarity results.
    """
    filtered_results = []

    # Group results by query image path and match type (image or text)
    query_groups = {}
    for result in results:
        query_path = result.get("query_image_path", "")
        match_type = result.get("match_type", "image")
        key = (query_path, match_type)
        if key not in query_groups:
            query_groups[key] = []
        query_groups[key].append(result)

    # Process each query group separately
    for (query_path, match_type), query_results in query_groups.items():
        # Sort by similarity score
        query_results.sort(key=lambda x: float(x.get("similarity", 0)), reverse=True)

        # Apply thresholds based on match type
        if match_type == "image":
            # Image similarity threshold: 0.6
            threshold = 0.6
            normalize_func = normalize_patent_image_score
        else:  # match_type == "text"
            # Text similarity threshold: 0.25
            threshold = 0.25
            normalize_func = normalize_patent_text_score

        # Filter results based on threshold
        for result in query_results:
            score = float(result.get("similarity", 0))
            normalized_score = normalize_func(score)

            # Store normalized score
            result["normalized_score"] = normalized_score

            if score >= threshold:
                # Check for top1 vs top2 difference
                if len(query_results) > 1:
                    top1_score = float(query_results[0].get("similarity", 0))
                    top2_score = float(query_results[1].get("similarity", 0))
                    similarity_over_top2 = top1_score - top2_score
                    similarity_over_top2_percentage = similarity_over_top2 / top1_score if top1_score > 0 else 0

                    # Add this information to the result
                    result["similarity_over_top2"] = f"{similarity_over_top2:.2f}"
                    result["similarity_over_top2_%"] = f"{similarity_over_top2_percentage:.2f}"

                    # If top1 is significantly better than top2 (>10% difference), boost its score
                    if similarity_over_top2_percentage > 0.1 and result == query_results[0]:
                        result["boosted"] = True

                filtered_results.append(result)

    # Remove duplicates based on ip_asset_id, preferring boosted results
    unique_results = {}
    for result in filtered_results:
        result_id = result.get("ip_asset_id", "")
        if result_id not in unique_results:
            unique_results[result_id] = result
        elif result.get("boosted", False) and not unique_results[result_id].get("boosted", False):
            # Prefer boosted results
            unique_results[result_id] = result
        elif float(result.get("similarity", 0)) > float(unique_results[result_id].get("similarity", 0)):
            # Otherwise prefer higher score
            unique_results[result_id] = result

    return list(unique_results.values())

from .normalization_service import normalize_trademark_score

def apply_trademark_similarity_filtering(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Apply similarity filtering logic for trademark results.

    Args:
        results: A list of trademark similarity results.

    Returns:
        A filtered list of trademark similarity results.
    """
    filtered_results = []

    # Group results by query image path
    query_groups = {}
    for result in results:
        query_path = result.get("query_image_path", "")
        if query_path not in query_groups:
            query_groups[query_path] = []
        query_groups[query_path].append(result)

    # Process each query group separately
    for query_path, query_results in query_groups.items():
        # Sort by similarity score (for EfficientNetB7, higher is better)
        query_results.sort(key=lambda x: float(x.get("similarity", 0)), reverse=True)

        # Apply similar logic to copyright for EfficientNetB7
        if query_results:
            # Get top1 score
            top1_score = float(query_results[0].get("similarity", 0))

            # Normalize the score
            normalized_top1_score = normalize_trademark_score(top1_score)

            # Store normalized score
            query_results[0]["normalized_score"] = normalized_top1_score

            # Top1 check (normalized score >= 1)
            if normalized_top1_score >= 1.0:
                filtered_results.append(query_results[0])

                # If there are more results, check for proximity
                if len(query_results) > 1:
                    proximity_threshold = top1_score - 0.02  # Use raw score for proximity
                    for result in query_results[1:]:
                        score = float(result.get("similarity", 0))
                        normalized_score = normalize_trademark_score(score)
                        result["normalized_score"] = normalized_score

                        if score >= proximity_threshold:
                            filtered_results.append(result)
                        else:
                            break

            # Top2/3 check (normalized score >= 1.13)
            for i in [1, 2]:
                if i < len(query_results):
                    result = query_results[i]
                    score = float(result.get("similarity", 0))
                    normalized_score = normalize_trademark_score(score)
                    result["normalized_score"] = normalized_score

                    if normalized_score >= 1.13:
                        filtered_results.append(result)

    # Remove duplicates based on ip_asset_id
    unique_results = {}
    for result in filtered_results:
        result_id = result.get("ip_asset_id", "")
        if result_id not in unique_results or float(result.get("similarity", 0)) > float(unique_results[result_id].get("similarity", 0)):
            unique_results[result_id] = result

    return list(unique_results.values())

def apply_similarity_filtering(results: List[Dict[str, Any]], ip_type: str) -> List[Dict[str, Any]]:
    """
    Apply similarity filtering logic based on IP type.

    Args:
        results: A list of similarity results.
        ip_type: The type of IP asset.

    Returns:
        A filtered list of similarity results.
    """
    if ip_type.lower() == "copyright":
        return apply_copyright_similarity_filtering(results)
    elif ip_type.lower() == "patent":
        return apply_patent_similarity_filtering(results)
    elif ip_type.lower() == "trademark":
        return apply_trademark_similarity_filtering(results)
    else:
        return results
