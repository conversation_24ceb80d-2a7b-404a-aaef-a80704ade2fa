import sqlite3
from datetime import datetime
import threading
import os
from contextlib import contextmanager
import re

# Thread-local storage
local = threading.local()

if os.name == 'nt':
    db_path = os.path.join(os.getcwd(), 'runs.db')
else:
    db_path = '/app/data/db/runs.db'

db_path = os.path.join(os.getcwd(), "data", 'runs.db')

@contextmanager
def task_context(run_id, step_name):
    set_context(run_id, step_name)
    try:
        yield
    finally:
        clear_context()

def set_context(run_id, step_name):
    local.run_id = run_id
    local.step_name = step_name

def clear_context():
    if hasattr(local, 'run_id'):
        del local.run_id
    if hasattr(local, 'step_name'):
        del local.step_name

def get_context():
    return getattr(local, 'run_id', None), getattr(local, 'step_name', None)

def log_message(message, level='INFO'):
    run_id, step_name = get_context()
    if "text_in_page" not in message and "page" not in message and "done" not in message:
        print(f"{level}: {message}")
    if run_id is None or step_name is None:
        # print(f"{level}: {message}")  # Fallback logging
        return

    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    timestamp = datetime.now().isoformat()
    c.execute('SELECT id FROM steps WHERE run_id=? AND name=?', (run_id, step_name))
    step_id = c.fetchone()[0]


    if "text_in_page" in message and "page" in message and "done" in message:

        # Extract filename from the message
        end_of_pdf = message.find(".pdf") + 4
        filename = message[:end_of_pdf]

        # Try to find an existing OCR log message for this step and filename
        c.execute('''
            SELECT id, message FROM logs 
            WHERE run_id=? AND step_id=? AND message LIKE ?
            ORDER BY id DESC LIMIT 1
        ''', (run_id, step_id, f'{filename}%'))
        existing_log = c.fetchone()

        if existing_log:
            # Update the existing log message
            existing_log_id, existing_log_message = existing_log  # Unpack both values
            new_page_number = int(existing_log_message[existing_log_message.find("page")+4:existing_log_message.find("done.")]) + 1
            message = message.replace(message[message.find("page")+4:message.find("done.")], str(new_page_number))
            c.execute('UPDATE logs SET message=?, timestamp=? WHERE id=?', 
                      (message, timestamp, existing_log_id))
        else:
            # Insert a new log message if no existing one is found
            message = message.replace(message[message.find("page")+4:message.find("done.")], " 1 ")
            c.execute('INSERT INTO logs (run_id, step_id, timestamp, level, message) VALUES (?, ?, ?, ?, ?)',
                      (run_id, step_id, timestamp, level, message))
    else:
        # Insert a new log message for non-OCR messages
        c.execute('INSERT INTO logs (run_id, step_id, timestamp, level, message) VALUES (?, ?, ?, ?, ?)',
                  (run_id, step_id, timestamp, level, message))

    conn.commit()
    conn.close()


# Helper functions for database operations
def create_new_run():
    
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    start_time = datetime.now().isoformat()
    c.execute('INSERT INTO runs (start_time, status) VALUES (?, ?)', (start_time, 'Running'))
    run_id = c.lastrowid
    steps = ['Get Cases', 'Process Pictures', 'Upload to Database', 'Upload Pictures', 'AI Tasks']
    for step in steps:
        c.execute('INSERT INTO steps (run_id, name, status) VALUES (?, ?, ?)', (run_id, step, 'Pending'))
    conn.commit()
    conn.close()
    print(f"Created new run id: {run_id}")
    return run_id

def update_step_status(run_id, step_name, status):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    timestamp = datetime.now().isoformat()
    if status == 'Running':
        c.execute('UPDATE steps SET status=?, start_time=? WHERE run_id=? AND name=?', (status, timestamp, run_id, step_name))
    else:
        c.execute('UPDATE steps SET status=?, end_time=? WHERE run_id=? AND name=?', (status, timestamp, run_id, step_name))
    conn.commit()
    conn.close()


def finish_run(run_id, status):
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    end_time = datetime.now().isoformat()
    c.execute('UPDATE runs SET status=?, end_time=? WHERE id=?', (status, end_time, run_id))
    conn.commit()
    conn.close()

def log_check_message(message, check_id, level='INFO'):
    timestamp = datetime.now().isoformat()

    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute('INSERT INTO check_logs (check_id, timestamp, level, message) VALUES (?, ?, ?, ?)',
                  (check_id, timestamp, level, message))
        conn.commit()
    except Exception as e:
        print(f"Error logging to database: {e}")
    finally:
        if conn:
            conn.close()


# Initialize the database
def init_db():
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS runs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    start_time TEXT,
                    end_time TEXT,
                    status TEXT
                )''')
    c.execute('''CREATE TABLE IF NOT EXISTS steps (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id INTEGER,
                    name TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    status TEXT,
                    FOREIGN KEY(run_id) REFERENCES runs(id)
                )''')
    c.execute('''CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id INTEGER,
                    step_id INTEGER,
                    timestamp TEXT,
                    level TEXT,
                    message TEXT,
                    FOREIGN KEY(run_id) REFERENCES runs(id),
                    FOREIGN KEY(step_id) REFERENCES steps(id)
                )''')
    c.execute('''CREATE TABLE IF NOT EXISTS check_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    check_id TEXT,
                    timestamp TEXT,
                    level TEXT,
                    message TEXT
                )''')
    conn.commit()
    conn.close()