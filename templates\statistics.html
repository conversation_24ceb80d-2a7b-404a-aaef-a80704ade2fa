<!DOCTYPE html>
<html>
<head>
    <title>Model Statistics</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <style>
        .matrix-cell { min-width: 80px; text-align: center; }
        .score-table { margin-bottom: 40px; }
        .matrix-table { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="my-4">Model Comparison Statistics</h1>
        
        <a href="{{ url_for('product_view') }}" class="btn btn-secondary mb-4">Back to Product View</a>

        <!-- Overall Scores -->
        <div class="score-table">
            <h3>Overall Scores (Weighted Sum)</h3>
            <table class="table table-bordered">
                <thead class="thead-light">
                    <tr>
                        {% for model in overall_scores %}
                        <th>Model {{ loop.index0 }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        {% for score in overall_scores.values() %}
                        <td>{{ "%.2f"|format(score) }}</td>
                        {% endfor %}
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Top-1 Scores -->
        <div class="score-table">
            <h3>Top-1 Scores</h3>
            <table class="table table-bordered">
                <thead class="thead-light">
                    <tr>
                        {% for model in top1_scores %}
                        <th>Model {{ loop.index0 }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        {% for score in top1_scores.values() %}
                        <td>{{ "%.2f"|format(score) }}</td>
                        {% endfor %}
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Correlation Matrix -->
        <div class="matrix-table">
            <h3>Model Correlation Matrix</h3>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th></th>
                        {% for model in overall_scores %}
                        <th>Model {{ loop.index0 }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for i in range(correlation_matrix|length) %}
                    <tr>
                        <th>Model {{ i }}</th>
                        {% for j in range(correlation_matrix[i]|length) %}
                        <td class="matrix-cell">{{ "%.2f"|format(correlation_matrix[i][j]) }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html> 