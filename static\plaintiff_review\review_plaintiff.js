document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const reviewTable = document.getElementById('review-table');
    const reviewTableBody = document.getElementById('review-table-body');
    const totalResults = document.getElementById('total-results');
    const refreshButton = document.getElementById('refresh-data');
    const submitButton = document.getElementById('submit-reviews');
    const add9AndLen2Button = document.getElementById('add-9-and-len2');
    const deleteWithoutCasesButton = document.getElementById('delete-plaintiff-without-cases');
    const deleteOverlay = document.getElementById('delete-overlay');
    const plaintiffsToDeleteList = document.getElementById('plaintiffs-to-delete-list');
    const cancelDeleteButton = document.getElementById('cancel-delete');
    const confirmDeleteButton = document.getElementById('confirm-delete');

    // Review data
    let reviewData = [];
    let plaintiffsToDelete = [];

    // Event listeners
    refreshButton.addEventListener('click', () => loadReviewData(true));
    submitButton.addEventListener('click', submitReviews);
    add9AndLen2Button.addEventListener('click', runImprove9AndLen2);
    deleteWithoutCasesButton.addEventListener('click', showPlaintiffsToDelete);
    cancelDeleteButton.addEventListener('click', () => deleteOverlay.style.display = 'none');
    confirmDeleteButton.addEventListener('click', confirmDeletePlaintiffs);

    // Load data on page load
    loadReviewData();

    function loadReviewData(forceRefresh = false) {
        document.querySelector('.loading').style.display = 'block';
        reviewTable.style.display = 'none';

        fetch(`/api/plaintiff/reviews?force_refresh=${forceRefresh}`)
            .then(response => response.json())
            .then(data => {
                reviewData = data.reviews || [];
                totalResults.textContent = `${reviewData.length} reviews`;

                renderData();
                document.querySelector('.loading').style.display = 'none';
                reviewTable.style.display = 'table';
            })
            .catch(error => {
                console.error('Error loading review data:', error);
                document.querySelector('.loading').textContent = 'Error loading data. Please try again.';
            });
    }

    function renderData() {
        // Clear table
        reviewTableBody.innerHTML = '';

        // Render all rows
        reviewData.forEach(review => {
            const row = document.createElement('tr');
            row.dataset.caseId = review.case_id;

            // Format the plaintiff_names from JSON if needed
            let plaintiffNames = review.plaintiff_names;
            if (plaintiffNames && typeof plaintiffNames === 'string') {
                try {
                    const names = JSON.parse(plaintiffNames);
                    plaintiffNames = Array.isArray(names) ? names.join(', ') : plaintiffNames;
                } catch (e) {
                    // Keep as is if not valid JSON
                }
            }

            // Format date
            const formattedDate = review.date_filed ? new Date(review.date_filed).toLocaleDateString() : '';
            const formattedUpdateTime = review.update_time ? new Date(review.update_time).toLocaleString() : '';

            row.innerHTML = `
                <td>${review.case_id}</td>
                <td>${formattedDate}</td>
                <td>${review.docket || ''}</td>
                <td>${review.current_plaintiff_name || ''}</td>
                <td class="plaintiff-names-cell truncate">${plaintiffNames || ''}</td>
                <td class="proposed-name-cell">${review.proposed_name || ''}</td>
                <td class="method-info-cell truncate">${review.method_info || ''}</td>
                <td>${formattedUpdateTime}</td>
                <td class="action-cell">
                    <div class="radio-option approve-option">
                        <input type="radio" name="action_${review.case_id}" value="approve" id="approve_${review.case_id}">
                        <label for="approve_${review.case_id}">Approve</label>
                    </div>
                    <div class="radio-option reject-option">
                        <input type="radio" name="action_${review.case_id}" value="reject" id="reject_${review.case_id}">
                        <label for="reject_${review.case_id}">Reject</label>
                    </div>
                </td>
            `;

            reviewTableBody.appendChild(row);
        });
    }

    function submitReviews() {
        const decisions = [];

        // Get all rows with radio buttons selected
        document.querySelectorAll('#review-table-body tr').forEach(row => {
            const caseId = row.dataset.caseId;
            const approveRadio = document.getElementById(`approve_${caseId}`);
            const rejectRadio = document.getElementById(`reject_${caseId}`);

            if (approveRadio.checked) {
                decisions.push({
                    case_id: caseId,
                    action: 'approve'
                });
            } else if (rejectRadio.checked) {
                decisions.push({
                    case_id: caseId,
                    action: 'reject'
                });
            }
        });

        if (decisions.length === 0) {
            alert('No actions selected. Please select at least one review to approve or reject.');
            return;
        }

        // Send decisions to server
        fetch('/api/plaintiff/reviews/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ decisions })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully processed ${data.processed_count} reviews.`);
                loadReviewData(); // Refresh the data
            } else {
                alert('Error processing reviews: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error submitting reviews:', error);
            alert('Error submitting reviews. Please try again.');
        });
    }

    function runImprove9AndLen2() {
        // Show loading state
        add9AndLen2Button.disabled = true;
        add9AndLen2Button.textContent = 'Processing...';

        // Call the API to run the improve_plaintiff_9_and_2_letter_names function
        fetch('/api/plaintiff/improve-9-and-len2', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully processed ${data.processed_count} cases. Added to review queue.`);
                loadReviewData(); // Refresh the data to show new reviews
            } else {
                alert('Error processing cases: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error running improve function:', error);
            alert('Error processing cases. Please try again.');
        })
        .finally(() => {
            // Reset button state
            add9AndLen2Button.disabled = false;
            add9AndLen2Button.textContent = 'Add #9 and len=2';
        });
    }

    function showPlaintiffsToDelete() {
        // Show loading state
        deleteWithoutCasesButton.disabled = true;
        deleteWithoutCasesButton.textContent = 'Loading...';

        // Get the list of plaintiffs without cases
        fetch('/api/plaintiff/without-cases')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                plaintiffsToDelete = data.plaintiffs || [];

                if (plaintiffsToDelete.length === 0) {
                    alert('No plaintiffs without cases found.');
                    return;
                }

                // Populate the overlay list
                plaintiffsToDeleteList.innerHTML = '';
                const listHTML = document.createElement('ul');

                plaintiffsToDelete.forEach(plaintiff => {
                    const item = document.createElement('li');
                    item.textContent = `ID: ${plaintiff.id} - ${plaintiff.plaintiff_name}`;
                    listHTML.appendChild(item);
                });

                plaintiffsToDeleteList.appendChild(listHTML);

                // Show the overlay
                deleteOverlay.style.display = 'flex';
            } else {
                alert('Error getting plaintiffs: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error getting plaintiffs without cases:', error);
            alert('Error getting plaintiffs. Please try again.');
        })
        .finally(() => {
            // Reset button state
            deleteWithoutCasesButton.disabled = false;
            deleteWithoutCasesButton.textContent = 'Delete plaintiff without cases';
        });
    }

    function confirmDeletePlaintiffs() {
        // Show loading state
        confirmDeleteButton.disabled = true;
        confirmDeleteButton.textContent = 'Deleting...';

        // Get the IDs of plaintiffs to delete
        const plaintiffIds = plaintiffsToDelete.map(p => p.id);

        // Call the API to delete the plaintiffs
        fetch('/api/plaintiff/delete-without-cases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ plaintiff_ids: plaintiffIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Successfully deleted ${data.deleted_count} plaintiffs.`);
                deleteOverlay.style.display = 'none';
            } else {
                alert('Error deleting plaintiffs: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting plaintiffs:', error);
            alert('Error deleting plaintiffs. Please try again.');
        })
        .finally(() => {
            // Reset button state
            confirmDeleteButton.disabled = false;
            confirmDeleteButton.textContent = 'Confirm Delete';
        });
    }
});
// Menu activation is handled by common.js activateMenuItem('menu-review-plaintiff')