import pandas as pd
import numpy as np
import json
from flask import request, jsonify, render_template
import cache_manager
from logdata import log_message
from auth_decorators import login_required

def init_tro_ip_routes(app):
    """Initialize routes related to TRO IP by plaintiff."""

    @app.route('/tro_ip')
    @login_required
    def tro_ip():
        """Render the TRO IP by plaintiff page."""
        # Ensure cache is loaded, no force needed just for page render
        cache_manager.refresh_cached_data(force=False)
        return render_template('tro_ip.html')

    @app.route('/api/plaintiffs/ip', methods=['GET'])
    def get_plaintiffs_ip():
        """Get plaintiffs IP data with filters applied."""
        log_message("Request received for /api/plaintiffs/ip", level="DEBUG")

        # Get filter parameters
        plaintiff_name = request.args.get('plaintiff_name', '').strip()
        plaintiff_id = request.args.get('plaintiff_id', '')
        picture_type = request.args.get('picture_type', '')  # Single selection dropdown
        ip_count_min = int(request.args.get('ip_count_min', 0))
        ip_count_max = request.args.get('ip_count_max', '')
        if ip_count_max and ip_count_max.isdigit():
            ip_count_max = int(ip_count_max)
        else:
            ip_count_max = None
        sort_by = request.args.get('sort_by', 'ip_count')
        sort_order = request.args.get('sort_order', 'desc')
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        refresh = request.args.get('refresh', 'false').lower() == 'true'

        try:
            # Ensure cache is loaded
            if not cache_manager.refresh_cached_data(force=refresh):
                log_message("Error: Cache not loaded in /api/plaintiffs/ip", level="ERROR")
                return jsonify({"error": "Data cache not available"}), 500

            # Check cache variables
            if (cache_manager.cached_plaintiff_df is None or
                cache_manager.cached_cases_df is None):
                log_message("Error: Required cached dataframes are None in /api/plaintiffs/ip", level="ERROR")
                return jsonify({"error": "Data cache unavailable"}), 500

            # Create a copy of the dataframes to work with
            plaintiff_df = cache_manager.cached_plaintiff_df.copy()
            cases_df = cache_manager.cached_cases_df.copy()

            # Apply filters to plaintiff dataframe
            if plaintiff_name:
                plaintiff_df = plaintiff_df[plaintiff_df['plaintiff_name'].str.contains(plaintiff_name, case=False, na=False)]

            if plaintiff_id:
                # Convert both the DataFrame column and the input to string for exact comparison
                plaintiff_df = plaintiff_df[plaintiff_df['id'].astype(str) == plaintiff_id]

            # Process IP data for each plaintiff
            results = []

            for _, plaintiff in plaintiff_df.iterrows():
                plaintiff_id = plaintiff['id']
                plaintiff_data = {
                    'id': plaintiff_id,
                    'plaintiff_name': plaintiff['plaintiff_name'],
                    'trademarks': [],
                    'patents': [],
                    'copyrights': []
                }

                # Get cases for this plaintiff
                plaintiff_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id]

                # Process IP data from each case
                for _, case in plaintiff_cases.iterrows():
                    if pd.isna(case.get('images')):
                        continue

                    images_data = case.get('images')

                    # Process trademarks
                    if (picture_type == 'trademarks' or picture_type == '') and 'trademarks' in images_data:
                        for key, value in images_data.get('trademarks', {}).items():
                            if not isinstance(value, dict):
                                continue

                            # Check if this trademark is already in the list (avoid duplicates)
                            reg_no = value.get('reg_no')
                            if reg_no:
                                # Convert to string if it's a list
                                if isinstance(reg_no, list):
                                    reg_no = reg_no[0] if reg_no else None

                                # Skip if we already have this registration number
                                if any(t.get('reg_no') == reg_no for t in plaintiff_data['trademarks']):
                                    continue

                            # Extract trademark data
                            trademark_data = {
                                'image': key,
                                'reg_no': reg_no,
                                'trademark_text': value.get('trademark_text'),
                                'int_cls': value.get('int_cls_list'),
                                'full_filename': value.get('full_filename')
                            }
                            plaintiff_data['trademarks'].append(trademark_data)

                    # Process patents
                    if (picture_type == 'patents' or picture_type == '') and 'patents' in images_data:
                        for key, value in images_data.get('patents', {}).items():
                            if not isinstance(value, dict):
                                continue

                            # Check if this patent is already in the list (avoid duplicates)
                            patent_number = value.get('patent_number')
                            if patent_number:
                                # Skip if we already have this patent number
                                if any(p.get('patent_number') == patent_number for p in plaintiff_data['patents']):
                                    continue

                            # Extract patent data
                            patent_data = {
                                'image': key,
                                'patent_number': patent_number,
                                'text': value.get('product_name'),
                                'inventors': value.get('inventors'),
                                'applicant': value.get('applicant'),
                                'assignee': value.get('assignee'),
                                'full_filename': value.get('full_filename')
                            }
                            plaintiff_data['patents'].append(patent_data)

                    # Process copyrights
                    if (picture_type == 'copyrights' or picture_type == '') and 'copyrights' in images_data:
                        for key, value in images_data.get('copyrights', {}).items():
                            if not isinstance(value, dict):
                                continue

                            # Check if this copyright is already in the list (avoid duplicates)
                            reg_no = value.get('reg_no')
                            if reg_no:
                                # Convert to string if it's a list
                                if isinstance(reg_no, list):
                                    reg_no = reg_no[0] if reg_no else None

                                # Skip if we already have this registration number
                                if any(c.get('reg_no') == reg_no for c in plaintiff_data['copyrights']):
                                    continue

                            # Extract copyright data
                            copyright_data = {
                                'image': key,
                                'reg_no': reg_no,
                                'title': value.get('title'),
                                'full_filename': value.get('full_filename')
                            }
                            plaintiff_data['copyrights'].append(copyright_data)

                # Calculate total IP count
                total_ip_count = len(plaintiff_data['trademarks']) + len(plaintiff_data['patents']) + len(plaintiff_data['copyrights'])
                plaintiff_data['total_ip_count'] = total_ip_count

                # Apply IP count filters
                if (ip_count_min <= total_ip_count) and (ip_count_max is None or total_ip_count <= ip_count_max):
                    # Apply IP type filter - only include plaintiffs that have the selected IP types
                    if picture_type == '' or (picture_type == 'trademarks' and len(plaintiff_data['trademarks']) > 0) or \
                       (picture_type == 'patents' and len(plaintiff_data['patents']) > 0) or \
                       (picture_type == 'copyrights' and len(plaintiff_data['copyrights']) > 0) or \
                       (picture_type == 'no_ip' and total_ip_count == 0):

                        # If a specific IP type is selected, clear the others
                        if picture_type in ['trademarks', 'patents', 'copyrights']:
                            for ip_type in ['trademarks', 'patents', 'copyrights']:
                                if ip_type != picture_type:
                                    plaintiff_data[ip_type] = []

                        results.append(plaintiff_data)

            # Sort results
            reverse_order = (sort_order.lower() == 'desc')
            if sort_by == 'ip_count':
                results.sort(key=lambda x: x['total_ip_count'], reverse=reverse_order)
            elif sort_by == 'plaintiff_name':
                results.sort(key=lambda x: (x['plaintiff_name'] or "").lower(), reverse=reverse_order)

            # Get total count before pagination
            total_count = len(results)

            # Apply pagination
            results = results[offset:offset+limit]

            log_message(f"Returning {len(results)} plaintiffs for /api/plaintiffs/ip", level="INFO")
            return jsonify({
                'plaintiffs': results,
                'total': total_count
            })

        except Exception as e:
            log_message(f"Error in /api/plaintiffs/ip: {e}", level="ERROR")
            import traceback
            traceback.print_exc()
            return jsonify({"error": "An internal error occurred"}), 500

    return app
