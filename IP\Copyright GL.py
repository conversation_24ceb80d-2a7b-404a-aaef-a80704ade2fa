import time
import random
import os
import urllib.parse
import undetected_chromedriver as uc
import pandas as pd
import re

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import StaleElementReferenceException

from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay

def close_feedback_modal(driver):
    """
    Checks if the feedback modal is present and, if so, clicks the close button.
    """
    try:
        modal_close_button = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((
                By.XPATH, 
                "//div[contains(@class, 'modal-content')]//button[contains(@class, 'close-modal')]"
            ))
        )
        move_mouse_to(driver, modal_close_button)
        modal_close_button.click()
        print("Feedback modal closed.")
        time.sleep(1)
    except Exception:
        # Modal not present; do nothing.
        pass

def parse_record_details(driver):
    """
    Scrapes details from a detailed record page.
    Returns a dictionary mapping field names to their extracted values.
    It looks for the <cd-record> element and then:
      - For each <li> element inside, it grabs the label from the <strong> tag.
      - If an inner <ul> exists, it concatenates the child <li> texts.
      - Otherwise, it removes the label text from the overall text.
    """
    details = {}
    try:
        cd_record = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "cd-record"))
        )
        # Explicitly wait for the first li element to be present
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//cd-record//li"))
        )
        li_elements = cd_record.find_elements(By.TAG_NAME, "li")
        for li in li_elements:
            try:
                label_elem = li.find_element(By.TAG_NAME, "strong")
                label = label_elem.text.strip().rstrip(":")
                # Try to see if there is a <ul> inside (for multi-value fields)
                try:
                    ul_elem = li.find_element(By.TAG_NAME, "ul")
                    li_texts = [item.text.strip() for item in ul_elem.find_elements(By.TAG_NAME, "li") if item.text.strip()]
                    value = ", ".join(li_texts)
                except Exception:
                    # Remove the label text from the full text
                    full_text = li.text.strip()
                    value = full_text.replace(label_elem.text, "").strip()
                details[label] = value
            except Exception:
                continue
    except Exception:
        pass
    return details

def format_record(details):
    """
    Maps the scraped details to the target structure.
    If any field is missing, an empty string is set.
    """

    return {
        "Registration Number / Date": details.get("Registration Number / Date", ""),
        "Type of Work": details.get("Type of Work", ""),
        "Title": details.get("Title", ""),
        "Application Title": details.get("Application Title", ""),
        "Date of Creation": details.get("Date of Creation", ""),
        "Date of Publication": details.get("Date of Publication", ""),
        "Copyright Claimant": details.get("Copyright Claimant", ""),
        "Authorship on Application": details.get("Authorship on Application", ""),
        "Rights and Permissions": details.get("Rights and Permissions", ""),
        "Description": details.get("Description", ""),
        "Nation of First Publication": details.get("Nation of First Publication", ""),
        "Names": details.get("Names", ""),
        "Basis of Claim": details.get("Basis of Claim", "")
    }

def scrape_copyright_records(start_date, end_date, df=None):
    """
    Navigates to the advanced search page of the Copyright Public Records site with
    custom start_date and end_date (e.g. "2025-01-01 00:00:00"). It loops over every search 
    result page, opens each record in a new tab via Control+Click, extracts the required 
    fields, closes the tab, and finally assembles all records into a pandas DataFrame.
    """
    base_url = "https://publicrecords.copyright.gov"
    # Wrap the start_date and end_date in quotes and URL-encode them.
    start_date_enc = urllib.parse.quote(f'"{start_date}"')
    end_date_enc = urllib.parse.quote(f'"{end_date}"')
    
    records = []
    page_number = 1
    record_per_page = 50
    
    driver = None  # Initialize driver to None

    while True:
        page_retries = 0
        while page_retries < 3:  # Retry the page up to 3 times
            try:
                # Check if the driver is still "alive" and recreate it if necessary
                if driver is None:
                    print("Driver is None.")
                    driver_alive = False
                else:
                    try:
                        driver.title  # Simple command to check if the driver is responsive
                        driver_alive = True
                    except Exception as e:
                        print("Error while trying to access driver.title:", e)
                        driver_alive = False

                if not driver_alive:
                    if driver is not None:
                        try:
                            driver.quit()  # Try to quit the old driver
                        except Exception:
                            pass  # Ignore any errors during quitting
                    print("Creating a new driver instance...")
                    driver = get_driver()

                # Build the advanced search URL for the current page.
                search_url = (
                    f"{base_url}/advanced-search?page_number={page_number}"
                    f"&parent_query=%5B%7B%22operator_type%22:%22AND%22,"
                    f"%22column_name%22:%22all_names%22,"
                    f"%22type_of_query%22:%22phrase%22,"
                    f"%22query%22:%222-D%20Artwork%22%7D%5D"
                    f"&records_per_page={record_per_page}"
                    f"&type_of_record=%5B%22registration%22%5D"
                    f"&sort_field=%22representative_date%22"
                    f"&sort_order=%22asc%22"
                    f"&highlight=true"
                    f"&registration_status=%5B%22published%22%5D"
                    f"&type_of_work=%5B%22visual_material%22%5D"
                    f"&model=%22%22"
                    f"&date_field=%22representative_date%22"
                    f"&start_date={start_date_enc}"
                    f"&end_date={end_date_enc}"
                )
                
                driver.get(search_url)

                # Wait for the search results table to be present.
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, "table-list-of-search-results"))
                )
    
                # Wait for the first row to be present.
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]"))
                )
                # Close any feedback modal if it appears.
                close_feedback_modal(driver)
            
                # Wait for the first row to have some text content before proceeding
                WebDriverWait(driver, 10).until(
                    lambda d: driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")[0].text.strip() != ""
                )
                
                # Fetch the rows AFTER closing the modal and waiting
                rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")

                print(f"Found {len(rows)} result rows on page {page_number}")
                
                if len(rows) < record_per_page:
                    print(f"Page {page_number} has less than {record_per_page} rows. Assuming it is the last page.")
                    last_page = True
                else:
                    last_page = False

                
                row_index = 0
                while row_index < len(rows):
                    row_retries = 0
                    row_processed = False  # Flag to indicate if the row has been processed or skipped
                    while row_retries < 3 and not row_processed:  # Retry the row up to 3 times
                        try:
                            # Check if driver is still valid before proceeding
                            try:
                                driver.current_url  # Simple check to verify driver is still working
                            except Exception as e:
                                print(f"Driver became invalid before processing row {row_index}: {e}")
                                # Recreate the driver and reload the page
                                try:
                                    driver.quit()
                                except:
                                    pass
                                driver = get_driver()
                                driver.get(search_url)
                                
                                # Wait for page to load and close modal if needed
                                WebDriverWait(driver, 10).until(
                                    EC.presence_of_element_located((By.ID, "table-list-of-search-results"))
                                )
                                WebDriverWait(driver, 10).until(
                                    EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]"))
                                )
                                close_feedback_modal(driver)
                                
                                # Re-fetch rows
                                rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")
                                continue  # Skip to next iteration of while loop
                            
                            # Locate the 'Full Title' link in the row.
                            a_link = rows[row_index].find_element(By.XPATH, ".//a[contains(@class, 'link')]")

                            reg_no_match = re.search(r"VA\d{10}", rows[row_index].text)
                            if reg_no_match:
                                reg_no = reg_no_match.group(0)
                                #print(f"reg_no: {reg_no}")  # Debugging

                                # Check if df is not None and the column exists
                                if df is not None and "Registration Number / Date" in df.columns:
                                    # Ensure the column contains strings
                                    df["Registration Number / Date"] = df["Registration Number / Date"].astype(str)

                                    # Check if reg_no is a valid string before using 'in'
                                    if isinstance(reg_no, str):
                                        #print(f"df['Registration Number / Date']: {df['Registration Number / Date'].tolist()}")  # Debugging
                                        if any(reg_no in item_reg_no for item_reg_no in df["Registration Number / Date"]):
                                            print(f"Registration number {reg_no} already scraped on row: {row_index}")
                                            row_processed = True  # Set the flag to True
                                            row_index += 1  # Move to the next row
                                            continue  # Skip to the next row
                                    else:
                                        print(f"Warning: reg_no is not a string: {reg_no}")
                                else:
                                    print("Warning: DataFrame is None or 'Registration Number / Date' column is missing.")

                        
                            # Scroll the link into view before clicking.
                            driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", a_link)

                            
                            # Use ActionChains to move to the element before clicking
                            actions = ActionChains(driver)
                            actions.move_to_element(a_link).perform()
                            time.sleep(0.5)  # Fixed delay of 0.5 seconds
                            actions.key_down(Keys.CONTROL).click(a_link).key_up(Keys.CONTROL).perform()
                            time.sleep(1.5)  # Increased delay after opening new tab
                            
                            # Check if a new tab was actually opened
                            if len(driver.window_handles) < 2:
                                raise Exception("New tab was not opened")
                            
                            # Switch to the newly opened tab.
                            driver.switch_to.window(driver.window_handles[-1])
                            
                            # Close modal in the detail page if it appears.
                            close_feedback_modal(driver)
                            
                            # Wait a bit for the page to fully load after closing modal
                            time.sleep(1)
                            
                            # Parse record details and map the fields.
                            details = parse_record_details(driver)
                            
                            # Check if details is empty, wait a bit and retry once
                            if not details or not details.get("Registration Number / Date"):
                                print("Details were empty, waiting a bit and retrying...")
                                time.sleep(2)  # Increased wait time
                                details = parse_record_details(driver)
                                
                                # If still empty after retry, log and continue
                                if not details or not details.get("Registration Number / Date"):
                                    print("Details still empty after retry. Skipping record.")
                            
                            record = format_record(details)
                            records.append(record)
                            
                            # Check if driver is still valid before trying to close the tab
                            try:
                                # Close detail tab and switch back to main search results.
                                driver.close()
                                driver.switch_to.window(driver.window_handles[0])
                            except Exception as e:
                                print(f"Error while closing tab: {e}")
                                # Recreate the driver and reload the page
                                try:
                                    driver.quit()
                                except:
                                    pass
                                driver = get_driver()
                                driver.get(search_url)
                                
                                # Wait for page to load and close modal if needed
                                WebDriverWait(driver, 10).until(
                                    EC.presence_of_element_located((By.ID, "table-list-of-search-results"))
                                )
                                close_feedback_modal(driver)
                                
                                # Re-fetch rows
                                rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")
                                # Still count as processed since we got the data
                            
                            row_processed = True # Set the flag to True if row was processed
                            print(f"Successfully processed row {row_index}")
                            
                        except Exception as e:
                            print(f"Error processing row {row_index} on page {page_number}:", e)

                            # Check if it's a session/driver error
                            if "invalid session id" in str(e) or "no such session" in str(e):
                                print("Driver session has become invalid. Recreating driver...")
                                try:
                                    driver.quit()
                                except:
                                    pass
                                
                                driver = get_driver()
                                driver.get(search_url)
                                
                                # Wait for page to load and close modal if needed
                                WebDriverWait(driver, 10).until(
                                    EC.presence_of_element_located((By.ID, "table-list-of-search-results"))
                                )
                                close_feedback_modal(driver)
                                
                                # Re-fetch rows
                                rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")
                            
                            row_retries += 1
                            if row_retries == 3:
                                print(f"Failed to process row {row_index} after 3 retries. Moving to next row.")
                                row_index += 1  # Move to the next row after 3 failures
                                break
                            else:
                                print(f"Retrying row {row_index} (Attempt {row_retries} of 3)...")

                                # Ensure we are on the correct tab by checking the URL or title
                                try:
                                    if "publicrecords.copyright.gov/detailed-record" in driver.current_url:
                                        print("Not on the correct tab. Closing the details tab.")
                                        driver.close()
                                        driver.switch_to.window(driver.window_handles[0])
                                except Exception as tab_error:
                                    print(f"Error while checking/switching tabs: {tab_error}")
                                    # This might be another invalid session error
                                    # Do nothing here as we'll handle invalid sessions at the beginning of the next iteration
                                
                                # Re-fetch the rows only if a StaleElementReferenceException occurs
                                # or if details were empty (which can also be a sign of stale elements)
                                if isinstance(e, StaleElementReferenceException) or "StaleElementReferenceException" in str(e):
                                    print("Re-fetching rows due to StaleElementReferenceException.")
                                    try:
                                        rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']//div[contains(@class,'search-result-pane') and not(contains(@class, 'footer'))]")
                                    except Exception as fetch_error:
                                        print(f"Error re-fetching rows: {fetch_error}")
                                        # This might be another invalid session error
                                        # Do nothing here as we'll handle invalid sessions at the beginning of the next iteration

                                time.sleep(2)  # Wait for 2 seconds before retrying
                        finally:
                            if row_processed:
                                row_index += 1  # Move to the next row only if the current one was processed
                
                break # Exit the page retry loop if we successfully processed the page

            except Exception as page_error:
                print(f"Something went wrong on page {page_number}: {page_error}")
                
                # Check if it's a session/driver error
                if "invalid session id" in str(page_error) or "no such session" in str(page_error):
                    print("Driver session has become invalid. Recreating driver...")
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = None  # Will be recreated at the beginning of the next iteration
                
                page_retries += 1
                if page_retries == 3:
                    print("Failed to scrape page after 3 retries.")
                    break # Exit the page retry loop
                
                time.sleep(3)  # Wait a bit before retrying the page
            
        if not last_page:
            page_number += 1
        else:
            break  # Exit the page loop if it's the last page

    try:
        driver.quit()
    except:
        pass
    
    df = pd.DataFrame(records)
    return df

if __name__ == "__main__":
    df_already_scraped = pd.read_csv("copyright_records.csv")
    #df = scrape_copyright_records("2023-10-17 00:00:00", "2023-10-17 00:00:00", df_already_scraped)
    df = scrape_copyright_records("2024-01-04 00:00:00", "2024-01-04 00:00:00", df_already_scraped)
    df_new = pd.concat([df_already_scraped, df])
    df_new.to_csv("copyright_records.csv", index=False)