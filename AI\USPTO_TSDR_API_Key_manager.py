import asyncio
import time
import random
from logdata import log_message # Assuming logdata.py is accessible

class ApiKeyManager:
    """
    Manages a pool of API keys, handling rotation and rate limiting
    for both 'heavy' and 'light' request types.
    """
    HEAVY_REQUEST_LIMIT_PER_MINUTE = 4
    LIGHT_REQUEST_LIMIT_PER_MINUTE = 60
    RATE_LIMIT_WINDOW_SECONDS = 60 # 1 minute window

    def __init__(self, api_keys):
        """
        Initializes the ApiKeyManager.

        Args:
            api_keys (list): A list of API key strings.
        """
        self.api_keys = [key for key in api_keys if key]  # Filter out None or empty keys
        if not self.api_keys:
            log_message("ApiKeyManager initialized with no valid API keys.", level='WARNING')
        
        # Each item: {'key': str, 'rate_limited_until': float (monotonic time), 'index': int,
        #             'heavy_request_timestamps': list, 'light_request_timestamps': list}
        # 'index' here is the original index from the input api_keys list.
        self._keys_status = [
            {'key': key_val,
             'rate_limited_until': 0.0,
             'index': i,
             'heavy_request_timestamps': [],
             'light_request_timestamps': []}
            for i, key_val in enumerate(self.api_keys)
        ]
        self._lock = asyncio.Lock()
        # Start with a random key to distribute load initially for the next selection attempt
        self._next_key_check_start_idx = random.randint(0, len(self.api_keys) - 1) if self.api_keys else 0

    async def get_key(self, request_type='light'):
        """
        Retrieves an available API key. If all keys are rate-limited (either by 429
        or proactive limits), it will asynchronously wait until one becomes available.

        Args:
            request_type (str): 'heavy' or 'light' to apply appropriate rate limits.

        Returns:
            tuple: (api_key_string, api_key_original_index) or (None, -1) if no keys are configured.
        """
        if not self.api_keys:
            return None, -1
        
        if request_type not in ['heavy', 'light']:
            log_message(f"ApiKeyManager: Invalid request_type '{request_type}'. Defaulting to 'light'.", level='WARNING')
            request_type = 'light'

        while True:  # Loop until a key is obtained
            async with self._lock:
                now = time.monotonic()
                min_wait_next_slot = float('inf') # Minimum time until any key might be usable for this request_type

                # Try to find an available key using a round-robin approach
                for i in range(len(self._keys_status)):
                    current_check_idx = (self._next_key_check_start_idx + i) % len(self._keys_status)
                    key_info = self._keys_status[current_check_idx]

                    # 1. Check if key is under a 429-induced rate limit hold
                    if now < key_info['rate_limited_until']:
                        min_wait_next_slot = min(min_wait_next_slot, key_info['rate_limited_until'] - now)
                        continue # Key is under 429 hold, try next key

                    # 2. Prune old timestamps from usage lists
                    key_info['heavy_request_timestamps'] = [
                        ts for ts in key_info['heavy_request_timestamps'] if now - ts < self.RATE_LIMIT_WINDOW_SECONDS
                    ]
                    key_info['light_request_timestamps'] = [
                        ts for ts in key_info['light_request_timestamps'] if now - ts < self.RATE_LIMIT_WINDOW_SECONDS
                    ]

                    # 3. Check proactive limits based on request_type
                    can_use_key = False
                    if request_type == 'heavy':
                        if len(key_info['heavy_request_timestamps']) < self.HEAVY_REQUEST_LIMIT_PER_MINUTE:
                            can_use_key = True
                        elif key_info['heavy_request_timestamps']: # If limit reached, calculate when next slot opens
                            min_wait_next_slot = min(min_wait_next_slot,
                                                     (key_info['heavy_request_timestamps'][0] + self.RATE_LIMIT_WINDOW_SECONDS) - now)
                    else: # 'light' request
                        if len(key_info['light_request_timestamps']) < self.LIGHT_REQUEST_LIMIT_PER_MINUTE:
                            can_use_key = True
                        elif key_info['light_request_timestamps']: # If limit reached, calculate when next slot opens
                             min_wait_next_slot = min(min_wait_next_slot,
                                                      (key_info['light_request_timestamps'][0] + self.RATE_LIMIT_WINDOW_SECONDS) - now)
                    
                    if can_use_key:
                        # Key is available. Record usage and return.
                        if request_type == 'heavy':
                            key_info['heavy_request_timestamps'].append(now)
                        else:
                            key_info['light_request_timestamps'].append(now)
                        
                        self._next_key_check_start_idx = (current_check_idx + 1) % len(self._keys_status)
                        log_message(f"ApiKeyManager: Providing API Key index {key_info['index']} ({key_info['key'][:4]}...) for {request_type} request.", level='DEBUG')
                        return key_info['key'], key_info['index']
                    
                    # If key cannot be used, min_wait_next_slot might have been updated. Continue to check other keys.

                # If loop completes, all keys are currently unavailable for this request_type.
                # min_wait_next_slot should hold the smallest calculated wait time.
                if min_wait_next_slot == float('inf'):
                    # This might happen if all keys are fine by proactive limits but somehow no key was selected (e.g. logic error)
                    # or if there are no keys at all (though caught earlier).
                    # Or if all keys are 429-limited and their rate_limited_until is far in future.
                    wait_duration = 1.0 # Default fallback wait
                    log_message(f"ApiKeyManager: min_wait_next_slot was inf. Using default wait: {wait_duration}s", level='WARNING')
                else:
                    wait_duration = max(0.1, min_wait_next_slot) # Ensure at least a small positive wait

            # Lock is released here. Wait outside the lock.
            log_message(f"\033[91mApiKeyManager: All API keys currently unavailable for {request_type} request. Waiting for {wait_duration:.2f} seconds.\033[0m", level='WARNING')
            await asyncio.sleep(wait_duration)
            # The outer 'while True' loop will continue, re-acquire the lock, and check keys again.

    async def report_rate_limit(self, api_key_original_index, retry_after_seconds=60):
        """
        Marks an API key as rate-limited due to a 429 response.

        Args:
            api_key_original_index (int): The original index of the API key that was rate-limited.
            retry_after_seconds (int): The duration in seconds to wait before this key can be used again.
        """
        if not self.api_keys or not (0 <= api_key_original_index < len(self.api_keys)):
            log_message(f"ApiKeyManager: Invalid API key index {api_key_original_index} reported for rate limit.", level='ERROR')
            return

        async with self._lock:
            # Find the key by its original index to update its status
            # Note: self._keys_status is ordered by original index if api_keys was not empty.
            if 0 <= api_key_original_index < len(self._keys_status):
                # Iterate to find the key_info dict that matches the original_index
                key_to_update = None
                for ki in self._keys_status:
                    if ki['index'] == api_key_original_index:
                        key_to_update = ki
                        break
                
                if key_to_update:
                    new_rate_limited_until = time.monotonic() + retry_after_seconds
                    # Only update if the new limit is further out than any existing one
                    if new_rate_limited_until > key_to_update['rate_limited_until']:
                         key_to_update['rate_limited_until'] = new_rate_limited_until
                         log_message(f"\033[91mApiKeyManager: API Key index {api_key_original_index} ({key_to_update['key'][:4]}...) marked as rate-limited until {time.strftime('%H:%M:%S', time.localtime(time.time() + retry_after_seconds))} (for {retry_after_seconds:.2f}s).\033[0m", level='WARNING')
                    else:
                         log_message(f"ApiKeyManager: API Key index {api_key_original_index} already has a rate limit until {time.strftime('%H:%M:%S', time.localtime(time.time() + (key_to_update['rate_limited_until'] - time.monotonic())))}. New request for {retry_after_seconds:.2f}s ignored.", level='DEBUG')

                else:
                    # This should not happen if api_key_original_index is valid
                    log_message(f"ApiKeyManager: Could not find API key with original index {api_key_original_index} in _keys_status to report rate limit.", level='ERROR')
            else:
                log_message(f"ApiKeyManager: API key original index {api_key_original_index} out of bounds for _keys_status.", level='ERROR')