"""
Forward check endpoint for comparing product images against IP assets.
"""

import uuid
from fastapi import APIRouter, Depends

from ..models.schemas import ForwardCheckRequest, ForwardCheckResponse, ProductInfringement, Infringement
from ..utils.auth import verify_token
from ..utils.db import get_ip_asset_metadata
from ..services.qdrant_service import (
    upsert_product_images,
    query_with_dynamic_limit,
    query_copyright_combined
)
from ..services.similarity_service import apply_similarity_filtering

router = APIRouter()

@router.post("/forward_check", response_model=ForwardCheckResponse, dependencies=[Depends(verify_token)])
async def forward_check(request: ForwardCheckRequest):
    """
    Forward Check endpoint.
    Compare user-submitted product images against the IP assets database.

    Args:
        request: The forward check request.

    Returns:
        The forward check response.
    """
    # Prepare products for upsertion
    products = []
    for product in request.products:
        # Generate a UUID if not provided
        product_id = product.id if product.id else str(uuid.uuid4())

        products.append({
            "id": product_id,
            "image_clip": product.image_clip,
            "image_efficientnet": product.image_efficientnet
        })

    # Upsert products into Product_Images collection
    upsert_product_images(request.client_id, request.check_id, products)

    # Use dynamic retrieval for each product
    all_results = []

    for product in request.products:
        product_results = []

        # Copyright search - use combined approach
        copyright_clip_results, copyright_efficientnet_results = query_copyright_combined(
            collection_name="IP_Assets",
            clip_vector=product.image_clip,
            efficientnet_vector=product.image_efficientnet,
            min_clip_threshold=0.5,  # Lower threshold for initial retrieval
            min_efficientnet_threshold=0.4,  # Lower threshold for initial retrieval
            max_results=200  # Get more results for better combination
        )

        # Add query path information for filtering
        for result in copyright_clip_results + copyright_efficientnet_results:
            result.payload["query_image_path"] = product.id if product.id else f"product_{len(all_results)}"
            result.payload["ip_type"] = "Copyright"

        # Patent image search
        patent_image_results = query_with_dynamic_limit(
            collection_name="IP_Assets",
            vector_name="patent_clip_image",
            query_vector=product.image_clip,
            min_score_threshold=0.4,  # Lower threshold for initial retrieval
            max_results=200
        )

        # Add approach and query path information
        for result in patent_image_results:
            result.payload["approach"] = "image"
            result.payload["match_type"] = "image"
            result.payload["query_image_path"] = product.id if product.id else f"product_{len(all_results)}"
            result.payload["ip_type"] = "Patent"

        # Patent text search
        patent_text_results = query_with_dynamic_limit(
            collection_name="IP_Assets",
            vector_name="patent_clip_text",
            query_vector=product.image_clip,
            min_score_threshold=0.2,  # Lower threshold for initial retrieval
            max_results=200
        )

        # Add approach and query path information
        for result in patent_text_results:
            result.payload["approach"] = "text"
            result.payload["match_type"] = "text"
            result.payload["query_image_path"] = product.id if product.id else f"product_{len(all_results)}"
            result.payload["ip_type"] = "Patent"

        # Trademark search
        trademark_results = query_with_dynamic_limit(
            collection_name="IP_Assets",
            vector_name="trademark_efficientnet",
            query_vector=product.image_efficientnet,
            min_score_threshold=0.5,  # Lower threshold for initial retrieval
            max_results=200
        )

        # Add query path information
        for result in trademark_results:
            result.payload["query_image_path"] = product.id if product.id else f"product_{len(all_results)}"
            result.payload["ip_type"] = "Trademark"

        # Combine all results for this product
        product_results.extend(copyright_clip_results)
        product_results.extend(copyright_efficientnet_results)
        product_results.extend(patent_image_results)
        product_results.extend(patent_text_results)
        product_results.extend(trademark_results)

        all_results.append((product, product_results))

    # Collect unique candidate IP Asset Point IDs
    candidate_ids = set()
    for product, product_results in all_results:
        for result in product_results:
            candidate_ids.add(result.id)

    # Get metadata for candidate IP assets
    ip_asset_metadata = get_ip_asset_metadata(candidate_ids)

    # Process results and apply filtering logic
    results = []

    for product, product_results in all_results:
        product_id = product.id if product.id else f"product_{len(results)}"

        # Collect potential infringements for this product
        potential_infringements = []

        # Process all results for this product
        for result in product_results:
            if result.id in ip_asset_metadata:
                # Get IP type from result payload or metadata
                ip_type = result.payload.get("ip_type", ip_asset_metadata[result.id]["type"].capitalize())

                # Create infringement entry
                infringement_entry = {
                    "ip_type": ip_type,
                    "ip_asset_id": result.id,
                    "score": result.score,
                    "metadata": ip_asset_metadata[result.id]["data"],
                    "approach": result.payload.get("approach", "unknown"),
                    "match_type": result.payload.get("match_type", "unknown"),
                    "query_image_path": result.payload.get("query_image_path", product_id)
                }

                # Check if this IP asset is already in potential_infringements
                existing = next((inf for inf in potential_infringements if inf["ip_asset_id"] == result.id), None)
                if existing:
                    # Update score if higher
                    if result.score > existing["score"]:
                        existing["score"] = result.score
                        existing["approach"] = infringement_entry["approach"]
                        existing["match_type"] = infringement_entry["match_type"]
                else:
                    potential_infringements.append(infringement_entry)

        # Apply similarity filtering logic for each IP type
        filtered_copyright_infringements = apply_similarity_filtering(
            [inf for inf in potential_infringements if inf["ip_type"] == "Copyright"],
            "copyright"
        )
        filtered_patent_infringements = apply_similarity_filtering(
            [inf for inf in potential_infringements if inf["ip_type"] == "Patent"],
            "patent"
        )
        filtered_trademark_infringements = apply_similarity_filtering(
            [inf for inf in potential_infringements if inf["ip_type"] == "Trademark"],
            "trademark"
        )

        # Combine filtered infringements
        filtered_infringements = filtered_copyright_infringements + filtered_patent_infringements + filtered_trademark_infringements

        # Sort by score in descending order
        filtered_infringements.sort(key=lambda x: x.get("normalized_score", x.get("score", 0)), reverse=True)

        # Convert to Pydantic models
        infringements = [
            Infringement(
                ip_type=inf["ip_type"],
                ip_asset_id=inf["ip_asset_id"],
                score=inf["score"],
                metadata=inf["metadata"]
            )
            for inf in filtered_infringements
        ]

        # Add to results if there are any infringements
        if infringements:
            results.append(ProductInfringement(
                input_product_id=product_id,
                potential_infringements=infringements
            ))

    return ForwardCheckResponse(results=results)
