import os
import sys
sys.path.append(os.getcwd())
import re
import json
import base64
import zlib
import numpy as np
from tqdm import tqdm
from DatabaseManagement.ImportExport import get_table_from_GZ
import cv2
from Check.RAG.Collect_Images import get_docket_from_filename, get_case_image_from_df

def build_copyright_images_embeddings_dataset(image_dir, cases_df, plaintiffs_df, npy_file_name):
    from Check.RAG.RAG_Inference import get_clipv2_embeddings, get_efficientnet_embeddings
    # List to store embeddings and image identifiers
    embeddings_b7 = []
    embeddings_clipv2 = []
    reg_nos = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    dockets = []
    number_of_cases_list = []

    # Get list of image files
    image_files = [f for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f))]

    existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name))

    # Process each image
    for img_file in tqdm(image_files, desc="Processing images"):
        try:
            img_path = os.path.join(image_dir, img_file)
            docket = get_docket_from_filename(img_file)
            if docket is None:
                continue

            case_row, case_image = get_case_image_from_df(cases_df, docket, img_file, "copyrights")
            plaintiff_id = case_row['plaintiff_id']
            if plaintiff_id == 9:
                continue

            if img_file in existing_structured_array['filename']:
                embeddings_b7.append(existing_structured_array[existing_structured_array['filename'] == img_file]['embedding_b7'])
                embeddings_clipv2.append(existing_structured_array[existing_structured_array['filename'] == img_file]['embedding_clipv2'])
            else:
                embeddings_b7.append(get_efficientnet_embeddings([img_path]))
                embeddings_clipv2.append(get_clipv2_embeddings([img_path], "image"))
        
            reg_no = case_image["reg_no"]
            reg_nos.append(reg_no)

            full_filename = case_image["full_filename"][0]
            full_filenames.append(full_filename)
            
            
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            plaintiff_names.append(plaintiff_name)

            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
            number_of_cases_list.append(number_of_cases)

            filenames.append(img_file)
            dockets.append(docket)
            
        except Exception as e:
            print(f"build_images_embeddings_dataset: Error processing {img_file}: {e}")

    
    # Convert lists to structured numpy array
    dimenssions_b7 = len(embeddings_b7[0][0])  # model.output_shape[1] for EfficientNetB7 does not work for ClipV2
    dimenssions_clipv2 = len(embeddings_clipv2[0][0])  # model.output_shape[1] for EfficientNetB7 does not work for ClipV2
    dtype = [('embedding_b7', 'float32', (dimenssions_b7,)), ('embedding_clipv2', 'float32', (dimenssions_clipv2,)), ('reg_no', 'U100'), ('filename', 'U250'), ('full_filename', 'U250'), ('plaintiff_name', 'U150'), ('docket', 'U100'), ('number_of_cases', 'int')]
    structured_array = np.zeros(len(embeddings_b7), dtype=dtype)
    
    for i in range(len(embeddings_b7)):
        structured_array[i] = (embeddings_b7[i], embeddings_clipv2[i], reg_nos[i], filenames[i], full_filenames[i], plaintiff_names[i], dockets[i], number_of_cases_list[i])

    # Save structured array to disk
    np.save(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), structured_array)



def build_patent_images_embeddings_dataset(image_dir, cases_df, plaintiffs_df, npy_file_name):
    from Check.RAG.RAG_Inference import get_clipv2_embeddings
    # List to store embeddings and image identifiers
    embeddings = []
    texts = []
    patent_numbers = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    dockets = []
    number_of_cases_list = []

    # Get list of image files
    image_files = [f for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f))]

    existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name))


    # Process each image
    for img_file in tqdm(image_files, desc="Processing images"):
        try:
            img_path = os.path.join(image_dir, img_file)
            docket = get_docket_from_filename(img_file)
            if docket is None:
                continue

            filename = img_file.split('_', 1)[1]
            case_row, case_image = get_case_image_from_df(cases_df, docket, filename, "patents")
            
            if filename in existing_structured_array['filename']:
                embeddings.append(existing_structured_array[existing_structured_array['filename'] == filename]['embedding'])
            else:
                print(f"🔥 Building embeddings for {img_file}")
                embeddings.append(get_clipv2_embeddings([img_path], "image"))

            patent_number = case_image["patent_number"]
            patent_numbers.append(patent_number)

            text = case_image["product_name"]
            texts.append(text)

            full_filename = case_image["full_filename"][0]
            full_filenames.append(full_filename)
            
            plaintiff_id = case_row['plaintiff_id']
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            plaintiff_names.append(plaintiff_name)

            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
            number_of_cases_list.append(number_of_cases)

            filenames.append(filename)
            dockets.append(docket)
            
        except Exception as e:
            print(f"build_images_embeddings_dataset: Error processing {img_file}: {e}")

    
    # Convert lists to structured numpy array
    dimenssions = len(embeddings[0][0])  # model.output_shape[1] for EfficientNetB7 does not work for ClipV2
    dtype = [('embedding', 'float32', (dimenssions,)), ('text', 'U200'), ('patent_number', 'U100'),('filename', 'U250'), ('full_filename', 'U250'), ('plaintiff_name', 'U150'), ('docket', 'U100'), ('number_of_cases', 'int')]
    structured_array = np.zeros(len(embeddings), dtype=dtype)
    
    for i in range(len(embeddings)):
        structured_array[i] = (embeddings[i], texts[i], patent_numbers[i], filenames[i], full_filenames[i], plaintiff_names[i], dockets[i], number_of_cases_list[i])

    # Save structured array to disk
    np.save(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), structured_array)


def build_patent_text_embeddings_dataset(cases_df, plaintiffs_df, npy_file_name):
    from Check.RAG.RAG_Inference import get_clipv2_embeddings
    # List to store embeddings and image identifiers
    embeddings = []
    patent_numbers = []
    full_filenames = []
    filenames = []
    texts = []
    plaintiff_names = []
    dockets = []
    number_of_cases_list = []

    existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), allow_pickle=True)

    # Get list of image files
    product_name_list = []
    for index, row in tqdm(cases_df.iterrows(), desc="Processing Patent Texts"):
        try:
            if "patents" in row['images']:
                for i, image in enumerate(row['images']['patents'].keys()):
                    if "product_name" in row['images']['patents'][image] and row['images']['patents'][image]['product_name'] != "":
                        text = row['images']['patents'][image]['product_name']
                
                        # Check if the text already exists before appending
                        if text not in texts and text != "" and text is not None:
                            patent_number = row['images']['patents'][image]['patent_number']
                            print(f"patent_number: {patent_number}, patent text: {text}, case: {row["docket"]}")
                            docket = row["docket"]
                            full_filename = row['images']['patents'][image]['full_filename'][0]
                            filename = [key for key in row['images']['patents'].keys() if row['images']['patents'][key]['full_filename'][0] == full_filename]
                            plaintiff_id = cases_df[cases_df['docket'] == docket]['plaintiff_id'].values[0]
                            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
                            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
                            
                            # Append embedding and metadata to lists
                            if text in existing_structured_array['text']:
                                embeddings.append(existing_structured_array[existing_structured_array['text'] == text]['embedding'])
                            else:
                                print(f"🔥 Building embeddings for {text}")
                                embeddings.append(get_clipv2_embeddings([text], "text"))
            

                            patent_numbers.append(patent_number)
                            full_filenames.append(full_filename)
                            filenames.append(filename) # Each item is a list of filenames
                            texts.append(text)
                            plaintiff_names.append(plaintiff_name)
                            dockets.append(docket)
                            number_of_cases_list.append(number_of_cases)
                    else: 
                        print(f"No product text for case:{row["docket"]}")
            
        except Exception as e:
            print(f"build_text_embeddings_dataset: Error processing {row['docket']}: {e}")
    
    # Convert lists to structured numpy array
    dimenssions = len(embeddings[0][0])  # model.output_shape[1] for EfficientNetB7 does not work for ClipV2
    dtype = [('embedding', 'float32', (dimenssions,)), ('text', 'U200'), ('patent_number', 'U100'), ('filename', 'O'), ('full_filename', 'U250'), ('plaintiff_name', 'U150'), ('docket', 'U100'), ('number_of_cases', 'int')]
    structured_array = np.zeros(len(embeddings), dtype=dtype)
    
    for i in range(len(embeddings)):
        structured_array[i] = (embeddings[i], texts[i], patent_numbers[i], filenames[i], full_filenames[i], plaintiff_names[i], dockets[i], number_of_cases_list[i])

    # Save structured array to disk
    np.save(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), structured_array)



def build_trademark_logo_descriptors_dataset(trademark_dir, cases_df, plaintiffs_df, npy_file_name):
    # Initialize the ORB detector
    orb = cv2.ORB_create()


    if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name)):
        existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), allow_pickle=True)
    else:
        existing_structured_array = None

    # Get list of image files
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')
    image_files = [f for f in os.listdir(trademark_dir) if f.lower().endswith(valid_extensions)]

    # Initialize metadata lists
    all_descriptors = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    plaintiff_ids = []
    dockets = []
    number_of_cases_list = []
    reg_nos = []
    int_cls_list = []

    # Process each image
    for img_file in tqdm(image_files, desc="Processing trademarks"):
        try:
            img_path = os.path.join(trademark_dir, img_file)
            
            # Extract docket from filename similar to other functions
            docket = get_docket_from_filename(img_file)
            if docket is None:
                continue

            case_row, case_image = get_case_image_from_df(cases_df, docket, img_file, "trademarks")
            if case_row is None:  # Add this check
                print(f"Warning: Could not retrieve case row for {img_file}. Skipping.")
                continue
            plaintiff_id = case_row['plaintiff_id']

            # Get trademark details
            reg_no = case_image["reg_no"]
            int_cls = case_image["int_cls_list"]
            full_filename = case_image["full_filename"]

            # Get case metadata
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]


            if existing_structured_array is not None and img_file in existing_structured_array['filename']:
                all_descriptors.append(existing_structured_array[existing_structured_array['filename'] == img_file]['descriptors'])
            else:
                # print(f"🔥 Building descriptors for {img_file}")
                # Process image
                img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if img is None:
                    print(f"Warning: Unable to read {img_file}. Skipping.")
                    continue

                # Detect keypoints and compute descriptors
                keypoints, descriptors = orb.detectAndCompute(img, None)
                if descriptors is None:
                    print(f"Warning: No descriptors found for {img_file}.")
                    continue

                all_descriptors.append(descriptors)

            # Store data
            filenames.append(img_file)
            full_filenames.append(full_filename)
            plaintiff_names.append(plaintiff_name)
            plaintiff_ids.append(plaintiff_id)
            dockets.append(docket)
            number_of_cases_list.append(number_of_cases)
            reg_nos.append(reg_no)
            int_cls_list.append(int_cls)

        except Exception as e:
            print(f"Error processing {img_file}: {e}")

    # Create structured array ("O" is for object)
    dtype = [('descriptors', 'O'), ('filename', 'U250'), ('full_filename', 'O'), ('plaintiff_name', 'U150'), ('plaintiff_id', 'int32'), ('docket', 'U100'), ('number_of_cases', 'int32'), ('reg_no', 'O'), ('int_cls_list', 'O')]
    
    structured_array = np.zeros(len(filenames), dtype=dtype)
    for i in range(len(filenames)):
        structured_array[i] = (all_descriptors[i], filenames[i], full_filenames[i], plaintiff_names[i], plaintiff_ids[i], dockets[i], number_of_cases_list[i], reg_nos[i], int_cls_list[i])

    # Save structured array
    np.save(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), structured_array)
    print(f"Trademark descriptors and metadata saved successfully")


# def build_trademark_logo_descriptors_dataset():
#     # Initialize the ORB detector
#     orb = cv2.ORB_create()

#     # Get list of image files in the trademarks directory
#     valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')
#     image_files = [f for f in os.listdir(trademark_dir) if f.lower().endswith(valid_extensions)]

#     # Initialize lists to store descriptors and image IDs
#     all_descriptors = []
#     all_image_ids = []

#     # Process each image
#     for img_file in tqdm(image_files, desc="Processing trademarks"):
#         try:
#             # Load image in grayscale mode
#             img_path = os.path.join(trademark_dir, img_file)
#             img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)

#             if img is None:
#                 print(f"Warning: Unable to read {img_file}. Skipping.")
#                 continue

#             # Detect keypoints and compute descriptors
#             keypoints, descriptors = orb.detectAndCompute(img, None)

#             if descriptors is not None:
#                 # Append descriptors and image filename
#                 all_descriptors.append(descriptors)
#                 all_image_ids.append(img_file)
#             else:
#                 print(f"Warning: No descriptors found for {img_file}.")
#         except Exception as e:
#             print(f"Error processing {img_file}: {e}")

#     # Save descriptors and image IDs to a single .npy file
#     np.save("data/EmbeddingsDescriptors/TrademarkDescriptors.npy", {'image_ids': all_image_ids, 'descriptors': all_descriptors})

#     print(f"All descriptors and image IDs saved to file")



def investigate_npy():
    existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsPatentImages.npy"), allow_pickle=True)
    existing_structured_array_old = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsPatentImages copy.npy"), allow_pickle=True)

    print(f"length of new: {len(existing_structured_array)}")
    print(f"length of old: {len(existing_structured_array_old)}")

    for i in range(len(existing_structured_array)):
        if existing_structured_array[i]['full_filename'] != existing_structured_array_old[i]['full_filename']:
            print(f"difference at {i}: {existing_structured_array[i]['full_filename']} != {existing_structured_array_old[i]['full_filename']}")
        if existing_structured_array[i]['full_filename'][-4:] != 'webp':
            print(f"not webp at {i}: {existing_structured_array[i]['full_filename']}")



if __name__ == "__main__":
    cases_df = get_table_from_GZ("tb_case", force_refresh=False)
    plaintiffs_df = get_table_from_GZ('tb_plaintiff', force_refresh=False)
    BASE_DIR = os.getcwd()

    # investigate_npy()

    ### Patent vector store build
    # PRODUCTION_FOLDER = os.path.join(BASE_DIR, "data", "IP", "patents", "Production")
    # build_patent_images_embeddings_dataset(PRODUCTION_FOLDER, cases_df, plaintiffs_df, "EmbeddingsPatentImages.npy")
    # build_patent_text_embeddings_dataset(cases_df, plaintiffs_df, "EmbeddingsPatentTexts.npy")

    ### Trademark vector store build
    PRODUCTION_FOLDER = os.path.join(BASE_DIR, "data", "IP", "trademarks", "Production")
    build_trademark_logo_descriptors_dataset(PRODUCTION_FOLDER, cases_df, plaintiffs_df, "TrademarkLogoDescriptors.npy")
    
    ### Copyright vector store build
    # PRODUCTION_FOLDER = os.path.join(BASE_DIR, "data", "IP", "copyrights", "Production")
    # build_copyright_images_embeddings_dataset(PRODUCTION_FOLDER, cases_df, plaintiffs_df, "EmbeddingsCopyright.npy")


    ### Test the copyright vector store
    # query_image_path = "D:/Win10User/Downloads/68_A_Stanislav Yurievich Osipov.jpg"
    # query_image_path = "D:/Win10User/Downloads/1_A_GOPRO_cropped.jpg"
    # results = find_most_similar_copyright(query_image_path, top_n=3, similarity_threshold=0.4)

    # if results:
    #     print("Top matches:")
    #     for match in results:
    #         print(f"Image: {match['filename']}")
    #         print(f"Similarity: {match['similarity']:.2f}")
    #         print(f"Plaintiff: {match['plaintiff_name']}")
    #         print(f"Docket: {match['docket']}")
    #         print(f"Total Cases: {match['number_of_cases']}")
    #         print("-" * 50)
    # else:
    #     print("No similar images found above the threshold.")