"""
Qdrant service for vector search operations.
"""

from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.http import models
from typing import List, Dict, Any, Optional
from ..utils.config import QDRANT_URL, QDRANT_API_KEY

# Initialize Qdrant client
qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)

def upsert_product_images(client_id: str, check_id: str, products: List[Dict[str, Any]]):
    """
    Upsert product images into the Product_Images collection.

    Args:
        client_id: The client ID.
        check_id: The check ID.
        products: A list of product image data.

    Returns:
        The result of the upsert operation.
    """
    points = []
    for product in products:
        # Create point
        point = models.PointStruct(
            id=product.get("id"),
            vectors={
                "image_clip": product.get("image_clip"),
                "image_efficientnet": product.get("image_efficientnet")
            },
            payload={
                "client_id": client_id,
                "check_id": check_id
            }
        )
        points.append(point)

    # Upsert points into Product_Images collection
    return qdrant_client.upsert(
        collection_name="Product_Images",
        points=points
    )

def upsert_ip_assets(ip_assets: List[Dict[str, Any]]):
    """
    Upsert IP assets into the IP_Assets collection.

    Args:
        ip_assets: A list of IP asset data.

    Returns:
        The result of the upsert operation.
    """
    points = []
    for ip_asset in ip_assets:
        # Create point
        vectors = {}
        if ip_asset.get("ip_type") == "Copyright":
            vectors["copyright_clip"] = ip_asset.get("vectors", {}).get("copyright_clip", [])
            vectors["copyright_efficientnet"] = ip_asset.get("vectors", {}).get("copyright_efficientnet", [])
        elif ip_asset.get("ip_type") == "Patent":
            vectors["patent_clip_image"] = ip_asset.get("vectors", {}).get("patent_clip_image", [])
            vectors["patent_clip_text"] = ip_asset.get("vectors", {}).get("patent_clip_text", [])
        elif ip_asset.get("ip_type") == "Trademark":
            vectors["trademark_efficientnet"] = ip_asset.get("vectors", {}).get("trademark_efficientnet", [])

        point = models.PointStruct(
            id=ip_asset.get("id"),
            vectors=vectors
        )
        points.append(point)

    # Upsert points into IP_Assets collection
    return qdrant_client.upsert(
        collection_name="IP_Assets",
        points=points
    )

def delete_points(collection_name: str, point_ids: List[str]):
    """
    Delete points from a collection.

    Args:
        collection_name: The name of the collection.
        point_ids: A list of point IDs to delete.

    Returns:
        The result of the delete operation.
    """
    return qdrant_client.delete(
        collection_name=collection_name,
        points_selector=models.PointIdsList(points=point_ids)
    )

def query_batch_points(collection_name: str, requests: List[models.QueryRequest]):
    """
    Execute a batch query against a collection.

    Args:
        collection_name: The name of the collection.
        requests: A list of query requests.

    Returns:
        The results of the batch query.
    """
    return qdrant_client.query_batch_points(
        collection_name=collection_name,
        requests=requests
    )

def query_with_dynamic_limit(collection_name: str, vector_name: str, query_vector: List[float],
                            min_score_threshold: float = 0.5, max_results: int = 200):
    """
    Execute a query with a dynamic limit based on score threshold.

    Args:
        collection_name: The name of the collection.
        vector_name: The name of the vector to query against.
        query_vector: The query vector.
        min_score_threshold: The minimum score threshold to include results.
        max_results: The maximum number of results to return.

    Returns:
        The filtered query results.
    """
    # First, retrieve a larger set of results
    results = qdrant_client.search(
        collection_name=collection_name,
        query_vector=(vector_name, query_vector),
        limit=max_results,  # Get a larger initial set
        score_threshold=min_score_threshold,  # Apply a minimum threshold
        with_payload=True,
        with_vectors=False
    )

    return results

def query_copyright_combined(collection_name: str, clip_vector: List[float], efficientnet_vector: List[float],
                           min_clip_threshold: float = 0.5, min_efficientnet_threshold: float = 0.4,
                           max_results: int = 200):
    """
    Execute a combined query for copyright using both CLIP and EfficientNet embeddings.

    Args:
        collection_name: The name of the collection.
        clip_vector: The CLIP query vector.
        efficientnet_vector: The EfficientNet query vector.
        min_clip_threshold: The minimum score threshold for CLIP results.
        min_efficientnet_threshold: The minimum score threshold for EfficientNet results.
        max_results: The maximum number of results to return per vector type.

    Returns:
        A tuple of (clip_results, efficientnet_results) with approach information added.
    """
    # Query with CLIP vector
    clip_results = query_with_dynamic_limit(
        collection_name=collection_name,
        vector_name="copyright_clip",
        query_vector=clip_vector,
        min_score_threshold=min_clip_threshold,
        max_results=max_results
    )

    # Add approach information
    for result in clip_results:
        result.payload["approach"] = "clipv2"

    # Query with EfficientNet vector
    efficientnet_results = query_with_dynamic_limit(
        collection_name=collection_name,
        vector_name="copyright_efficientnet",
        query_vector=efficientnet_vector,
        min_score_threshold=min_efficientnet_threshold,
        max_results=max_results
    )

    # Add approach information
    for result in efficientnet_results:
        result.payload["approach"] = "b7"

    return clip_results, efficientnet_results
