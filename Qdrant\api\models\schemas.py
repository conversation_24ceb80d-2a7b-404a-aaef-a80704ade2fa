"""
Pydantic models for request and response validation.
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

# Constants for vector dimensions
CLIP_DIMENSION = 1024
CLIP_TEXT_DIMENSION = 1024
EFFNET_DIMENSION = 2560

# Request models
class ProductImage(BaseModel):
    """Model for a product image with its vector embeddings."""
    id: Optional[str] = None
    image_clip: List[float]
    image_efficientnet: List[float]

class ForwardCheckRequest(BaseModel):
    """Request model for the forward check endpoint."""
    client_id: str
    check_id: str
    products: List[ProductImage]

class IPAsset(BaseModel):
    """Model for an IP asset with its metadata and vector embeddings."""
    ip_type: str
    id: str
    metadata: Dict[str, Any]
    vectors: Dict[str, List[float]]

class ReverseCheckRequest(BaseModel):
    """Request model for the reverse check endpoint."""
    ip_assets: List[IPAsset]

class DeletePointsRequest(BaseModel):
    """Request model for the delete points endpoint."""
    collection_name: str
    point_ids: List[str]

# Response models
class Infringement(BaseModel):
    """Model for an infringement result."""
    ip_type: str
    ip_asset_id: str
    score: float
    metadata: Dict[str, Any]

class ProductInfringement(BaseModel):
    """Model for product infringement results."""
    input_product_id: str
    potential_infringements: List[Infringement]

class ForwardCheckResponse(BaseModel):
    """Response model for the forward check endpoint."""
    results: List[ProductInfringement]

class ProductPointInfringement(BaseModel):
    """Model for product point infringement results."""
    product_point_id: str
    client_id: str
    check_id: str
    score: float

class IPAssetInfringement(BaseModel):
    """Model for IP asset infringement results."""
    input_ip_asset_id: str
    potential_infringements: List[ProductPointInfringement]

class ReverseCheckResponse(BaseModel):
    """Response model for the reverse check endpoint."""
    results: List[IPAssetInfringement]

class DeletePointsResponse(BaseModel):
    """Response model for the delete points endpoint."""
    status: str
    operation_id: int
    result: bool
