#!/usr/bin/env python3
"""
Weekly Patent Report

This script handles the downloading, processing, and database loading of USPTO patent data.
It processes weekly Patent Grant Full Text Data (PTGRDT) and monthly Cooperative Patent Classification (CPC) data.

Supported Modes:
- Grants:
    - latest: Process the most recent weekly grant file.
    - catchup: Process grant files from a start date to an end date.
    - specific_date: Process the grant file for a specific Tuesday date.
- CPC:
    - latest: Process the most recent monthly CPC file.
    - catchup: Process CPC files from a start month/year to an end month/year.
    - specific_file: Process a specific CPC file URL or path. # TODO: Implement specific_file if needed
"""

import os
import asyncio
import logging
import argparse
import datetime
import tempfile
import time
from pathlib import Path
from dotenv import load_dotenv
from dateutil.relativedelta import relativedelta, MO, TU, WE, TH, FR, SA, SU # For finding specific weekdays

# Import local modules
from patent_file import download_file, extract_patent_zip, send_to_nas, send_folder_to_nas
from patent_parser import parse_patent_xml_parallel
from patent_db import upsert_patent_grants, update_patents_with_cpc
from patent_image import process_patent_images
from Common.Constants import local_ip_folder, nas_ip_folder

# Load environment variables from .env file
load_dotenv()

# Configure logging
log_file_path = Path(__file__).parent / "weekly_patent_report.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Constants ---
# Base URLs (API endpoints)
# Grant files are typically released on Tuesdays. Filename format: IYYYYMMDD.zip
PTGRDT_API_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/{}/I{}.tar"
# CPC files are released monthly, often near the end. Filename format: CPCMCPT_YYYYMMDD.zip (Need to confirm exact format/date)
# Let's assume it uses the release date for now.
CPCMCPT_API_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/CPCMCPT/CPCMCPT_{}.zip" # Placeholder - need confirmation

# Directory structure
BASE_DIR = Path(local_ip_folder) / "Patents"
GRANT_ZIP_DIR = BASE_DIR / "USPTO_Grants" / "Zip"
GRANT_EXTRACT_DIR_BASE = BASE_DIR / "USPTO_Grants" / "Extracted" # Base for extracted content, organized by zip name
CPC_ZIP_DIR = BASE_DIR / "USPTO_CPC" / "Zip"
CPC_EXTRACT_DIR_BASE = BASE_DIR / "USPTO_CPC" / "Extracted" # Base for extracted content, organized by zip name

# Create local directories
GRANT_ZIP_DIR.mkdir(parents=True, exist_ok=True)
GRANT_EXTRACT_DIR_BASE.mkdir(parents=True, exist_ok=True)
CPC_ZIP_DIR.mkdir(parents=True, exist_ok=True)
CPC_EXTRACT_DIR_BASE.mkdir(parents=True, exist_ok=True)

# NAS paths (using f-strings for dynamic creation if needed, ensure base exists)
NAS_BASE_DIR = f"{nas_ip_folder}/Patents"
NAS_GRANT_ZIP_DIR = f"{NAS_BASE_DIR}/USPTO_Grants/Zip"
NAS_GRANT_EXTRACT_BASE = f"{NAS_BASE_DIR}/USPTO_Grants/Extracted"
NAS_CPC_ZIP_DIR = f"{NAS_BASE_DIR}/USPTO_CPC/Zip"
NAS_CPC_EXTRACT_BASE = f"{NAS_BASE_DIR}/USPTO_CPC/Extracted"

# --- Helper Functions ---

def get_last_tuesday(ref_date=None):
    """Finds the date of the most recent Tuesday relative to ref_date."""
    if ref_date is None:
        ref_date = datetime.date.today()
    # relativedelta(weekday=TU(-1)) finds the previous Tuesday
    return ref_date + relativedelta(weekday=TU(-1))

def get_tuesdays_in_range(start_date, end_date):
    """Generates all Tuesdays within a given date range (inclusive)."""
    current_date = start_date + relativedelta(weekday=TU(0)) # Find first Tuesday on or after start_date
    if current_date < start_date: # If start_date itself is Tuesday, TU(0) might go to next week
         current_date = start_date + relativedelta(weekday=TU(-1)) # Check previous Tuesday
         if current_date < start_date: # If previous is still before, start is after Tuesday, find next
             current_date = start_date + relativedelta(weekday=TU(+1))

    while current_date <= end_date:
        yield current_date
        current_date += datetime.timedelta(weeks=1)

# TODO: Implement robust functions for CPC date calculation if needed
# This likely requires knowing the exact release schedule (e.g., last Thursday)
def get_latest_cpc_release_date(ref_date=None):
    """
    Placeholder: Determines the filename date for the latest CPC file.
    Needs logic based on actual USPTO release schedule (e.g., last Thursday of month).
    """
    if ref_date is None:
        ref_date = datetime.date.today()
    # Example: Assume release is last Thursday of the *previous* month
    first_of_current_month = ref_date.replace(day=1)
    last_day_of_previous_month = first_of_current_month - datetime.timedelta(days=1)
    # Find the last Thursday of that previous month
    last_thursday = last_day_of_previous_month + relativedelta(weekday=TH(-1))
    logger.warning("Using placeholder logic for latest CPC date (last Thursday of previous month). Verify USPTO schedule.")
    return last_thursday

def get_cpc_dates_in_range(start_date, end_date):
    """
    Placeholder: Generates expected CPC release dates within a range.
    Needs logic based on actual USPTO release schedule.
    """
    logger.warning("Using placeholder logic for CPC date range. Verify USPTO schedule.")
    current_target_month = start_date.replace(day=1)
    while current_target_month <= end_date:
        # Example: Assume release is last Thursday of the month
        first_of_next_month = current_target_month + relativedelta(months=1)
        last_day_of_current_month = first_of_next_month - datetime.timedelta(days=1)
        last_thursday = last_day_of_current_month + relativedelta(weekday=TH(-1))
        if last_thursday >= start_date and last_thursday <= end_date:
             yield last_thursday
        current_target_month += relativedelta(months=1)


# --- Core Processing Function ---

async def process_patent_zip(zip_url, zip_filename, local_zip_dir, local_extract_base, nas_zip_dir, nas_extract_base, headers, record_type):
    """
    Downloads, extracts, parses, processes images, updates DB, and transfers a single patent ZIP file (Grant or CPC).

    Args:
        zip_url (str): URL to download the ZIP file.
        zip_filename (str): Expected filename of the ZIP.
        local_zip_dir (Path): Local directory to save the downloaded ZIP.
        local_extract_base (Path): Local base directory for temporary extraction.
        nas_zip_dir (str): NAS directory path to send the ZIP file.
        nas_extract_base (str): NAS base directory path for extracted files.
        headers (dict): Headers for the download request (e.g., API key).
        record_type (str): Type of records in the zip ('grant' or 'cpc').

    Returns:
        tuple: (processed_record_count, processed_image_count)
    """
    start_time = time.time()
    local_zip_path = local_zip_dir / zip_filename
    processed_records = 0
    processed_images = 0
    nas_zip_transfer_task = None
    nas_extract_transfer_task = None
    db_task = None

    logger.info(f"--- Starting processing for {zip_filename} ({record_type}) ---")

    # 1. Download File
    logger.info(f"Attempting download from: {zip_url}")
    try:
        download_success = await asyncio.to_thread(download_file, zip_url, str(local_zip_path), headers)
        if not download_success:
            logger.warning(f"File not found or download failed: {zip_url}. Skipping.")
            return 0, 0
        logger.info(f"Downloaded successfully to: {local_zip_path}")
    except Exception as e:
        logger.error(f"Error during download of {zip_filename}: {e}", exc_info=True)
        return 0, 0

    # Use a temporary directory for extraction to ensure cleanup
    with tempfile.TemporaryDirectory(prefix=f"patent_{record_type}_extract_", dir=str(local_extract_base)) as temp_extract_dir_str:
        temp_extract_dir = Path(temp_extract_dir_str)
        logger.info(f"Using temporary extraction directory: {temp_extract_dir}")

        try:
            # 2. Start NAS Transfer for ZIP (in background)
            nas_zip_path = f"{nas_zip_dir}/{zip_filename}"
            logger.info(f"Starting NAS transfer for ZIP: {local_zip_path} -> {nas_zip_path}")
            nas_zip_transfer_task = asyncio.create_task(
                asyncio.to_thread(send_to_nas, str(local_zip_path), nas_zip_path),
                name=f"NAS_Zip_{zip_filename}"
            )

            # 3. Extract ZIP contents (XMLs, Images) into temp dir
            logger.info(f"Extracting {zip_filename} to {temp_extract_dir}...")
            # extract_patent_zip returns a tuple: (list_of_full_paths, unique_extract_dir_path)
            # We only need the list of full paths here. The unique dir path is managed by the context manager.
            extracted_xml_full_paths, _ = await asyncio.to_thread(extract_patent_zip, str(local_zip_path), str(temp_extract_dir))
            # removed list comprehension as extract_patent_zip now returns full paths

            if not extracted_xml_full_paths:
                logger.warning(f"No XML files found or extraction failed for {zip_filename}. Check ZIP structure, extraction logic, or logs for errors like BadZipFile.")
                # Continue to allow NAS transfer completion, but skip further processing
            else:
                logger.info(f"Found {len(extracted_xml_full_paths)} XML files for parsing.")

                # 4. Parse XMLs in Parallel
                logger.info("Starting parallel XML parsing...")
                # parse_patent_xml_parallel uses ProcessPoolExecutor internally
                # Running it in a thread allows the main async loop to proceed
                parsed_records, image_filenames_from_xml = await asyncio.to_thread(
                    parse_patent_xml_parallel, extracted_xml_full_paths, record_type
                )
                processed_records = len(parsed_records)
                logger.info(f"Parsed {processed_records} records from {len(extracted_xml_full_paths)} XML files.")
                logger.info(f"Identified {len(image_filenames_from_xml)} potential image files from XML data.")

                if not parsed_records:
                    logger.warning(f"No records obtained after parsing XMLs from {zip_filename}.")
                    # Continue to allow NAS transfer completion
                else:
                    # 5. Process Images (Verify existence, update records) - Synchronous for now
                    # This function needs the directory where images were extracted (temp_extract_dir)
                    # and the list of records to potentially update.
                    logger.info("Starting image processing/verification...")
                    updated_records, processed_images = process_patent_images(
                        str(temp_extract_dir), parsed_records, image_filenames_from_xml
                    )
                    logger.info(f"Verified/Processed {processed_images} image files.")
                    # Use updated_records for database operations
                    parsed_records = updated_records # Replace original list with updated one

                    # 6. Database Operations (in background)
                    logger.info("Starting database operations...")
                    if record_type == 'grant':
                        db_task = asyncio.create_task(
                            asyncio.to_thread(upsert_patent_grants, parsed_records),
                            name=f"DB_Grant_{zip_filename}"
                        )
                    elif record_type == 'cpc':
                        # Assuming CPC records only update existing grants
                        db_task = asyncio.create_task(
                            asyncio.to_thread(update_patents_with_cpc, parsed_records),
                            name=f"DB_CPC_{zip_filename}"
                        )
                    else:
                        logger.error(f"Unknown record_type '{record_type}' for database operation.")

            # 7. Start NAS Transfer for Extracted Folder (in background)
            # Create a unique NAS destination path based on the zip filename
            zip_name_without_ext = Path(zip_filename).stem
            nas_extract_path = f"{nas_extract_base}/{zip_name_without_ext}"
            logger.info(f"Starting NAS transfer for extracted folder: {temp_extract_dir} -> {nas_extract_path}")
            nas_extract_transfer_task = asyncio.create_task(
                asyncio.to_thread(send_folder_to_nas, str(temp_extract_dir), nas_extract_path),
                name=f"NAS_Extract_{zip_filename}"
            )

            # 8. Await Background Tasks
            logger.info("Waiting for background tasks (NAS Zip, DB, NAS Extract)...")
            tasks_to_await = [t for t in [nas_zip_transfer_task, db_task, nas_extract_transfer_task] if t]
            if tasks_to_await:
                results = await asyncio.gather(*tasks_to_await, return_exceptions=True)
                for i, res in enumerate(results):
                    task_name = tasks_to_await[i].get_name() if hasattr(tasks_to_await[i], 'get_name') else f"Task-{i}"
                    if isinstance(res, Exception):
                        logger.error(f"Background task {task_name} failed: {res}", exc_info=res)
                    else:
                        logger.info(f"Background task {task_name} completed successfully.")
            else:
                logger.info("No background tasks were initiated.")


        except Exception as e:
            logger.error(f"Error during processing of {zip_filename} within temp dir {temp_extract_dir}: {e}", exc_info=True)
            # Ensure background tasks are awaited even if main processing fails
            logger.info("Attempting to await background tasks after error...")
            tasks_to_await = [t for t in [nas_zip_transfer_task, db_task, nas_extract_transfer_task] if t and not t.done()]
            if tasks_to_await:
                 await asyncio.gather(*tasks_to_await, return_exceptions=True) # Log potential errors during await

    # Temporary directory `temp_extract_dir` is automatically cleaned up here by the 'with' statement

    end_time = time.time()
    duration = end_time - start_time
    logger.info(f"--- Finished processing {zip_filename} in {duration:.2f} seconds. Records: {processed_records}, Images: {processed_images} ---")
    return processed_records, processed_images

# --- Workflow Functions ---

async def process_grants_for_period(start_date, end_date, headers):
    """Processes weekly grant files within a date range."""
    logger.info(f"Processing Grant files from {start_date} to {end_date}")
    total_records = 0
    total_images = 0
    processed_files = 0

    for target_date in get_tuesdays_in_range(start_date, end_date):
        date_str = target_date.strftime("%Y%m%d")
        zip_filename = f"I{date_str}.tar"
        zip_url = PTGRDT_API_BASE_URL.format(target_date.year, date_str)

        logger.info(f"\n=== Processing Grant File for Date: {target_date} ===")
        try:
            records, images = await process_patent_zip(
                zip_url=zip_url,
                zip_filename=zip_filename,
                local_zip_dir=GRANT_ZIP_DIR,
                local_extract_base=GRANT_EXTRACT_DIR_BASE,
                nas_zip_dir=NAS_GRANT_ZIP_DIR,
                nas_extract_base=NAS_GRANT_EXTRACT_BASE,
                headers=headers,
                record_type='grant'
            )
            total_records += records
            total_images += images
            if records > 0 or images > 0: # Count file if processing yielded results
                 processed_files += 1
        except Exception as e:
            logger.error(f"Failed to process grant file {zip_filename} for date {target_date}: {e}", exc_info=True)

    logger.info(f"\n=== Grant Processing Summary ({start_date} to {end_date}) ===")
    logger.info(f"Processed {processed_files} grant files.")
    logger.info(f"Total records processed: {total_records}")
    logger.info(f"Total images processed: {total_images}")
    logger.info("==========================================================")
    return total_records, total_images

async def process_cpc_for_period(start_date, end_date, headers):
    """Processes monthly CPC files within a date range."""
    logger.info(f"Processing CPC files potentially released between {start_date} and {end_date}")
    total_records = 0
    processed_files = 0

    # Use the placeholder function to get potential release dates
    for target_date in get_cpc_dates_in_range(start_date, end_date):
        date_str = target_date.strftime("%Y%m%d")
        # Confirm actual filename format - using placeholder
        zip_filename = f"CPCMCPT_{date_str}.zip"
        zip_url = CPCMCPT_API_BASE_URL.format(date_str)

        logger.info(f"\n=== Processing CPC File for Estimated Date: {target_date} ===")
        try:
            # CPC processing doesn't involve images in the same way grants do
            records, _ = await process_patent_zip(
                zip_url=zip_url,
                zip_filename=zip_filename,
                local_zip_dir=CPC_ZIP_DIR,
                local_extract_base=CPC_EXTRACT_DIR_BASE,
                nas_zip_dir=NAS_CPC_ZIP_DIR,
                nas_extract_base=NAS_CPC_EXTRACT_BASE,
                headers=headers,
                record_type='cpc'
            )
            total_records += records
            if records > 0:
                processed_files += 1
        except Exception as e:
            logger.error(f"Failed to process CPC file {zip_filename} for date {target_date}: {e}", exc_info=True)

    logger.info(f"\n=== CPC Processing Summary ({start_date} to {end_date}) ===")
    logger.info(f"Processed {processed_files} CPC files.")
    logger.info(f"Total records processed: {total_records}")
    logger.info("========================================================")
    return total_records, 0 # Return 0 for images for CPC

# --- Main Function & Argparse ---

async def main():
    """Main function to parse arguments and orchestrate processing."""
    parser = argparse.ArgumentParser(description="USPTO Patent Data Processing Orchestrator")
    parser.add_argument("--type", choices=["grant", "cpc"], required=True,
                        help="Type of data to process: weekly grants or monthly CPC.")
    parser.add_argument("--mode", choices=["latest", "catchup", "specific_date"], required=True,
                        help="Processing mode: 'latest', 'catchup' (requires date range), 'specific_date' (requires --date).")
    parser.add_argument("--start-date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="Start date for 'catchup' mode (YYYY-MM-DD).")
    parser.add_argument("--end-date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="End date for 'catchup' mode (YYYY-MM-DD). Defaults to today if not provided.")
    parser.add_argument("--date", type=lambda d: datetime.datetime.strptime(d, "%Y-%m-%d").date(),
                        help="Specific date for 'specific_date' mode (YYYY-MM-DD). Should be a Tuesday for grants.")
    # parser.add_argument("--file", type=str, help="Specific file URL or path for 'specific_file' mode.") # Optional: Add if specific file mode is needed

    args = parser.parse_args()

    # Setup API headers
    api_key = os.getenv("USPTO_ODP_API_KEY") # Using the same key as trademarks for now
    if not api_key:
        logger.warning("USPTO_ODP_API_KEY not found in .env file. Downloads may fail.")
        headers = None
    else:
        headers = {"X-API-KEY": api_key}

    start_time = time.time()

    if args.type == "grant":
        if args.mode == "latest":
            target_date = get_last_tuesday()
            logger.info(f"Processing latest grant file (estimated: {target_date})")
            await process_grants_for_period(target_date, target_date, headers)
        elif args.mode == "catchup":
            if not args.start_date:
                parser.error("--start-date is required for catchup mode.")
            end_date = args.end_date if args.end_date else datetime.date.today()
            if args.start_date > end_date:
                 parser.error("--start-date cannot be after --end-date.")
            await process_grants_for_period(args.start_date, end_date, headers)
        elif args.mode == "specific_date":
            if not args.date:
                parser.error("--date is required for specific_date mode.")
            # Optional: Add check if date is a Tuesday
            if args.date.weekday() != 1: # Monday is 0, Tuesday is 1
                 logger.warning(f"Specified date {args.date} is not a Tuesday. Grant files are typically released on Tuesdays.")
            await process_grants_for_period(args.date, args.date, headers)

    elif args.type == "cpc":
        if args.mode == "latest":
            target_date = get_latest_cpc_release_date() # Uses placeholder logic
            logger.info(f"Processing latest CPC file (estimated release date: {target_date})")
            await process_cpc_for_period(target_date, target_date, headers)
        elif args.mode == "catchup":
            if not args.start_date:
                parser.error("--start-date is required for catchup mode.")
            end_date = args.end_date if args.end_date else datetime.date.today()
            if args.start_date > end_date:
                 parser.error("--start-date cannot be after --end-date.")
            await process_cpc_for_period(args.start_date, end_date, headers)
        elif args.mode == "specific_date":
             if not args.date:
                 parser.error("--date is required for specific_date mode.")
             logger.warning("Processing CPC for a specific date assumes that date corresponds to a release file.")
             await process_cpc_for_period(args.date, args.date, headers)
        # elif args.mode == "specific_file": # Optional
        #     if not args.file:
        #         parser.error("--file is required for specific_file mode.")
        #     # TODO: Implement logic to handle specific file URL or path

    end_time = time.time()
    total_duration = end_time - start_time
    logger.info(f"\n=== Total Script Execution Time: {total_duration:.2f} seconds ===")


async def test_processing():
    """
    Test function to process a specific date (2025-03-15).
    """
    startdate = datetime.date(2025, 3, 15)
    enddate = datetime.date(2025, 3, 22)
    logger.info(f"Running test processing for {startdate} to {enddate}...")

    # Setup API headers
    # api_key = os.getenv("USPTO_BULK_API_KEY")
    api_key = os.getenv("USPTO_ODP_API_KEY")
    headers = {"X-API-KEY": api_key} if api_key else None

    # Process the test date directly
    processed = await process_grants_for_period(startdate, enddate, headers)

    logger.info(f"Test processing completed. Records processed: {processed}")
    return processed


if __name__ == "__main__":
    asyncio.run(test_processing())
    # asyncio.run(main())