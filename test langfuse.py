from Common.LangfuseClient import langfuse_client
from langfuse.decorators import observe, langfuse_context # Added langfuse
# from langfuse.model import ObservationType # Added for type checking

@observe(capture_input=False, capture_output=False) # test() remains decorated
def test():
    print("hi")
    # Printing IDs to help verify correct parenting
    print(f"Test's current trace_id: {langfuse_context.get_current_trace_id()}")
    print(f"Test's current observation_id: {langfuse_context.get_current_observation_id()}")
    # Since test() is decorated with capture_input=False, capture_output=False,
    # we set its input/output using langfuse_context.update_current_observation() here.
    langfuse_context.update_current_observation(
        input="example_test_input",
        output="example_test_output"
    )
    
def test_add_to_existing_trace():
    """
    Tests adding an observation to an existing trace and span.
    1. Retrieves all traces named "main_execution_trace".
    2. Selects the most recent one.
    3. Finds a span named "identify_case_and_navigate" within that trace.
    4. Calls test() to add a new observation parented under the found span.
    """
    print("\n--- Running test_add_to_existing_trace ---")
    
    # 1. Retrieve all traces with name "main_execution_trace"
    try:
        traces_response = langfuse_client.fetch_traces(name="main_execution_trace2", limit=100) # Adjust limit if necessary
        all_matching_traces = traces_response.data
    except Exception as e:
        print(f"Error retrieving traces: {e}")
        return

    if not all_matching_traces:
        print("No traces found with name 'main_execution_trace'. Ensure main() has run first to create one.")
        return

    # 2. Keep only the latest (most recent one)
    all_matching_traces.sort(key=lambda t: t.timestamp, reverse=True)
    latest_trace_summary = all_matching_traces[0]
    print(f"Found latest trace summary: ID={latest_trace_summary.id}, Timestamp={latest_trace_summary.timestamp}")

    # Fetch the full trace details to get observations
    retrieved_trace = langfuse_client.fetch_trace(id=latest_trace_summary.id)
    if not retrieved_trace:
        print(f"Could not retrieve full trace details for ID: {latest_trace_summary.id}")
        return
    print(f"Retrieved full trace: Name='{retrieved_trace.data.name}', ID='{retrieved_trace.data.id}'")

    # 3. Find the span_identify_case in that trace
    target_span = None
    if retrieved_trace.data.observations:
        for obs in retrieved_trace.data.observations:
            if obs.type == "SPAN" and obs.name == "identify_case_and_navigate":
                target_span = obs
                break
    
    if not target_span:
        print(f"Span 'identify_case_and_navigate' not found in trace {retrieved_trace.data.id}.")
        # Optionally, print available observations for debugging:
        # if retrieved_trace.observations:
        #     print("Available observations in the trace:")
        #     for obs_item in retrieved_trace.observations:
        #         print(f"  - Name: {obs_item.name}, Type: {obs_item.type}, ID: {obs_item.id}")
        return
    print(f"Found target span: Name='{target_span.name}', ID='{target_span.id}'")

    # 4. Call test() with the ID of the existing trace and span_identify_case
    print(f"Calling test() to add an observation to trace_id='{retrieved_trace.data.id}' and parent_observation_id='{target_span.id}'")
    test(
        langfuse_parent_trace_id=retrieved_trace.data.id, 
        langfuse_parent_observation_id=target_span.id
    )
    print("test() called. Check Langfuse UI for the new observation under the existing trace/span.")
    print("--- Finished test_add_to_existing_trace ---")

def main():
    print("hello")
    manual_trace = None
    span_identify_case = None
    try:
        # Manually create a trace for the main execution
        manual_trace = langfuse_client.trace(name="main_execution_trace2")
        
        # Create span_identify_case as a child of the manual_trace
        # Its parent_observation_id will be the root observation of manual_trace
        span_identify_case = manual_trace.span(
            name="identify_case_and_navigate",
            input="some_input_value_for_identify_case"
        )
        
        # Call test(), passing the manual_trace.id and span_identify_case.id
        # The @observe decorator on test() now respect these parenting IDs
        test(
            langfuse_parent_trace_id=manual_trace.id, 
            langfuse_parent_observation_id=span_identify_case.id
        )
    finally:
        # Ensure spans and traces are ended
        if span_identify_case:
            span_identify_case.end(output={"info": "some_output_value_for_identify_case", "info2": "some_output_value_for_identify_case2"})
        # StatefulTraceClient (manual_trace) does not have an .end() method.
        # The trace is concluded by the Langfuse client's flushing mechanism.
        # if manual_trace:
        #     manual_trace.end() # This line is removed
        
    
    
    
main()
# Call the new function to test adding to an existing trace
# test_add_to_existing_trace()