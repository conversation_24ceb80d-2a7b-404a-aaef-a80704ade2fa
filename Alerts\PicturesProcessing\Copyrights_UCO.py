import os
import sys
from typing import Optional
sys.path.append(os.getcwd())
import os
from logdata import log_message
from Alerts.PicturesProcessing.Copyrights_Google import get_copyright_images_from_google
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json
from langfuse.decorators import observe, langfuse_context
from IP.Copyright_UCO import get_info_from_UCO_using_reg_no, find_best_search_combination
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from typing import List
from IP.Copyright_UCO import scrape_USCO # For by_name search
from Alerts.Plaintiff import generate_search_terms_for_ip # For by_name search
import Common.Constants as Constants
from Alerts.Chrome_Driver import get_driver

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


@observe(capture_input=False, capture_output=False)
def get_copyright_art_info(df, index, reg_nos_to_check: List[str], ip_manager: IPTrackingManager, pdf_path_context: Optional[str] = None):
    """
    For a list of copyright registration numbers, attempts to find artist/title information.
    First, optionally uses LLM with a context PDF. Then, falls back to scraping USCO.
    Updates IPTrackingManager with found information (typically as 'pictures_not_found' if image itself isn't available).

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        reg_nos_to_check: List of registration numbers to get info for.
        ip_manager: Instance of IPTrackingManager.
        pdf_path_context: Optional path to a PDF to use as context for LLM.
        langfuse_parent_trace_id: Optional Langfuse trace ID.
        langfuse_parent_observation_id: Optional Langfuse parent observation ID.
    """
    if not reg_nos_to_check:
        log_message("get_info_from_usco: No registration numbers provided to check.", level="INFO")
        return

    log_message(f"Attempting to find claimant/title for {len(reg_nos_to_check)} reg_nos.", level="INFO")
    
    reg_nos_still_missing_details = list(reg_nos_to_check) # Work on a copy

    # Stage 1: Try LLM with PDF context if provided
    if pdf_path_context and os.path.exists(pdf_path_context) and reg_nos_still_missing_details:
        log_message(f"Using LLM with PDF context {os.path.basename(pdf_path_context)} for {len(reg_nos_still_missing_details)} reg_nos.", level="INFO")
        prompt_llm_context = """
        You are an expert in copyright registration numbers.
        You are given a list of copyright registration numbers and a document related to the court filings. For each number, you need to find the artist name (often called "Author") and the artwork name (often called "Title of work") in the document.
        You return your answer in a JSON format with the following keys: {
        """
        for reg_no_ctx in reg_nos_still_missing_details:
            prompt_llm_context += f'"{reg_no_ctx}": ["artist_name", "artwork_name"], '
        prompt_llm_context += "}. Do not include REDACTED registration numbers in your answer. If a registration number has no associated artist/artwork name in this document, leave its list empty []. These are the documents: "

        prompt_list_ctx = [("text", prompt_llm_context), ("pdf_path", pdf_path_context)]
        ai_answer_ctx = vertex_genai_multi(prompt_list_ctx, model_name=Constants.SMART_MODEL_FREE)
        ai_answer_json_ctx = get_json(ai_answer_ctx)

        if isinstance(ai_answer_json_ctx, dict):
            reg_nos_found_by_llm_ctx = []
            for reg_no, item_list in ai_answer_json_ctx.items():
                if reg_no in reg_nos_still_missing_details and isinstance(item_list, list) and len(item_list) == 2:
                    artist_name, artwork_name = item_list
                    artist_name = str(artist_name) if artist_name is not None else ""
                    artwork_name = str(artwork_name) if artwork_name is not None else ""
                    
                    log_message(f"LLM (context PDF) found for {reg_no}: Artist='{artist_name}', Title='{artwork_name}'", level="DEBUG")
                    ip_manager.add_picture_not_found('copyright', reg_no, artist_name, artwork_name)
                    reg_nos_found_by_llm_ctx.append(reg_no)
            
            reg_nos_still_missing_details = [r for r in reg_nos_still_missing_details if r not in reg_nos_found_by_llm_ctx]
        else:
            log_message(f"LLM (context PDF) did not return a valid JSON dict. Proceeding to USCO for all remaining.", level="WARNING")

    # Stage 2: Fallback to USCO for remaining numbers
    if reg_nos_still_missing_details:
        log_message(f"Checking USCO for details on {len(reg_nos_still_missing_details)} remaining reg_nos...", level="INFO")
        for reg_no_usco in reg_nos_still_missing_details:
            usco_info = get_info_from_UCO_using_reg_no(reg_no_usco)
            if usco_info and isinstance(usco_info, dict):
                artist = str(usco_info.get("Copyright Claimant", ""))
                title = str(usco_info.get("Title", ""))
                log_message(f"Found USCO details for {reg_no_usco}: Artist='{artist}', Title='{title}'", level="INFO")
                ip_manager.add_picture_not_found('copyright', reg_no_usco, artist, title)
            else:
                log_message(f"Could not find USCO details for {reg_no_usco}. Adding placeholders.", level="INFO")
                ip_manager.add_picture_not_found('copyright', reg_no_usco, "", "")
    else:
        log_message("No registration numbers remaining for USCO check.", level="INFO")

    langfuse_context.update_current_observation(
        input={
            "CaseIndex": index,
            "NumRegNosToCheck": len(reg_nos_to_check),
            "PDFContextProvided": pdf_path_context is not None
        },
        output={
            "CopyrightPicturesNotFound:": ip_manager.get_pictures_not_found('copyright')
        }
    )


@observe(capture_input=False, capture_output=False)
async def get_copyright_data_by_name(df, index, case_images_directory, plaintiff_df, ip_manager: IPTrackingManager):
    """
    Searches for copyrights by plaintiff name using USCO scraping and Google Images.

    Note: This function now manages the Selenium driver.
    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        plaintiff_df: DataFrame containing plaintiff information.
        ip_manager: Instance of IPTrackingManager to track IP processing state.
    """
    log_message(f"        Initiating copyright search by name for case index {index}...", level='INFO')
    plaintiff_id = df.at[index, "plaintiff_id"]
    main_plaintiff_name_row = plaintiff_df[plaintiff_df["id"] == plaintiff_id]

    if main_plaintiff_name_row.empty:
        log_message(f"        - Main Plaintiff ID {plaintiff_id} not found in plaintiff_df. Cannot effectively search by name.", level='WARNING')
        return False # Or handle as appropriate
    main_plaintiff_name = main_plaintiff_name_row["plaintiff_name"].values[0]

    plaintiff_names_json_str = df.at[index, "plaintiff_names"]
    final_search_terms = generate_search_terms_for_ip(main_plaintiff_name, plaintiff_names_json_str)

    if not final_search_terms:
        log_message(f"        - No suitable search terms generated for copyright search by name.", level='INFO')
        return False

    driver = None
    copyright_df = None
    processed_google_count = 0
    best_search_params = None

    try:
        driver = get_driver() # Initialize driver here

        # Find the best search combination
        best_search_params = find_best_search_combination(driver=driver,final_search_terms=final_search_terms)

        if best_search_params:
            log_message(f"        - Performing full scrape using best combination: Name='{best_search_params['name']}', Column='{best_search_params['column_name']}', Type='{best_search_params['type_of_query']}'", level='INFO')
            # Perform the full scrape using the best parameters
            copyright_df = scrape_USCO(
                driver=driver, # Pass the existing driver
                name=best_search_params['name'],
                column_name=best_search_params['column_name'],
                type_of_query=best_search_params['type_of_query'],
                ip_manager=ip_manager,
                max_results=100 # Still limit total results scraped if needed
            )

            # Now search Google for the found USCO records. "get copyright images from google" will update ip_manager if images are found
            processed_google_count = await get_copyright_images_from_google(df, index, case_images_directory, ip_manager=ip_manager)

            # If USCO found text or Google found images, mark for review
            if len(copyright_df) > 0 or processed_google_count > 0:
                 df.at[index, 'validation_status'] = 'review_required'
        else:
            log_message(f"        - No suitable copyright records found on USCO after evaluating combinations.", level='INFO')

    except Exception as e:
        log_message(f"❌❌ Error during copyright search by name for case index {index}: {e}", level="ERROR")
        # Decide how to handle errors - maybe set validation_status to error or skip
        df.at[index, 'validation_status'] = 'error' # Example: mark as error
    finally:
        if driver is not None:
            driver.quit() # Ensure driver is closed

    # Log results to Langfuse
    langfuse_context.update_current_observation(
        input={"SearchTerms": list(final_search_terms)},
        output={
            "BestSearchCombination": best_search_params if best_search_params else "N/A",
            "USCOResultsCount": len(copyright_df) if copyright_df is not None else 0,
            "GoogleImagesProcessedCount": processed_google_count,
            "CopyrightGoalMetAfter": ip_manager.is_goal_met('copyright'),
            "IPTrackingState": ip_manager._state
        }
    )

    # Return True if any results were found (either USCO text or Google images)
    return processed_google_count > 0