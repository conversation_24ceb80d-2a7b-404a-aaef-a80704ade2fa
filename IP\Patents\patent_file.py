"""
Patent File Management Module

This module provides functions to manage patent files from USPTO, 
including downloading, extracting XML from specific structures,
and sending to NAS.
"""

import os
import zipfile
import tarfile # Added for tar file handling
import requests
import logging
import time
import shutil
import tempfile
import io
from pathlib import Path
from FileManagement.NAS import NASConnection  # Assuming NASConnection is importable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("patent_file.log"), # Consider making log file configurable
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def download_file(url, local_path, headers=None, max_retries=5, retry_delay=5):
    """
    Download a file from a URL with retry logic.

    Args:
        url (str): URL to download from
        local_path (str): Path to save the file
        headers (dict, optional): HTTP headers
        max_retries (int): Maximum number of retry attempts
        retry_delay (int): Delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Attempting to download file from {url} to {local_path}")
    local_path_obj = Path(local_path)
    # Create directory if it doesn't exist
    local_path_obj.parent.mkdir(parents=True, exist_ok=True)

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, stream=True, timeout=120) # Increased timeout
            response.raise_for_status()

            # Save file
            # Log URL and path *after* successfully opening the file handle
            logger.info(f"Downloading from {url} to {local_path}")
            with open(local_path_obj, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Get file size after download
            try:
                file_size = os.path.getsize(local_path_obj)
                logger.info(f"Download complete for {local_path}. File size: {file_size} bytes.")
            except OSError as size_err:
                logger.error(f"Could not get size of downloaded file {local_path}: {size_err}")
                # Proceed to validation anyway, maybe it's still readable
                file_size = -1 # Indicate unknown size

            # --- Validate downloaded archive file ---
            file_extension = local_path_obj.suffix.lower()
            try:
                if file_extension == '.zip':
                    logger.info(f"Validating downloaded zip file: {local_path}")
                    with zipfile.ZipFile(local_path_obj, 'r') as zip_ref:
                        test_result = zip_ref.testzip() # Check for corrupt files within the archive
                        if test_result is not None:
                            raise zipfile.BadZipFile(f"First corrupt file in zip: {test_result}")
                    logger.info(f"ZIP Validation successful for {local_path}")
                    return True # Download and validation successful
                elif file_extension == '.tar':
                    logger.info(f"Validating downloaded tar file: {local_path}")
                    if not tarfile.is_tarfile(local_path_obj):
                         # is_tarfile doesn't raise specific errors easily, so we raise our own conceptually similar one
                        raise tarfile.ReadError(f"File {local_path} is not a valid tar file or is corrupted.")
                    # Optional: Could try opening and reading a member for more thorough validation
                    # with tarfile.open(local_path_obj, 'r') as tar_ref:
                    #     tar_ref.getmembers() # Try reading member list
                    logger.info(f"TAR Validation successful for {local_path}")
                    return True # Download and validation successful
                else:
                    logger.warning(f"Downloaded file {local_path} has an unsupported extension '{file_extension}'. Skipping validation.")
                    # Decide if unsupported extensions should be treated as success or failure.
                    # Assuming success for now, as download itself completed.
                    return True

            except (zipfile.BadZipFile, tarfile.ReadError) as archive_err:
                logger.error(f"Validation failed: Downloaded file {local_path} (size: {file_size} bytes) is invalid or corrupt: {archive_err}")
                try:
                    os.remove(local_path_obj)
                    logger.info(f"Deleted invalid downloaded file: {local_path}")
                except OSError as del_err:
                    logger.error(f"Failed to delete invalid downloaded file {local_path}: {del_err}")
                return False # Indicate download failure due to invalid archive
            except Exception as val_err: # Catch other potential errors during validation
                logger.error(f"An unexpected error occurred during archive validation for {local_path} (size: {file_size} bytes): {val_err}", exc_info=True)
                try:
                    os.remove(local_path_obj)
                    logger.info(f"Deleted file due to validation error: {local_path}")
                except OSError as del_err:
                    logger.error(f"Failed to delete file {local_path} after validation error: {del_err}")
                return False # Indicate failure

        except requests.exceptions.RequestException as e:
            status_code = getattr(e.response, 'status_code', None)
            if status_code == 404:
                logger.warning(f"File not found (404) at URL: {url}")
                return False # Don't retry on 404

            logger.warning(f"Download attempt {attempt + 1}/{max_retries} failed for {url}: {str(e)}")

            if attempt < max_retries - 1:
                logger.info(f"Retrying download in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                logger.error(f"Failed to download file {url} after {max_retries} attempts.")
                return False
    return False # Should not be reached, but added for clarity

def extract_patent_zip(archive_path, extract_base_dir):
    """
    Extracts XML files from USPTO patent archives (.zip for CPCMCPT, .tar for PTGRDT).

    Handles the nested structure of PTGRDT (.tar contains .zip files in DESIGN/UTIL,
    which in turn contain the .xml) and the direct XML structure of CPCMCPT (.zip
    contains .xml files directly). Extracts all found XML files into a unique
    subdirectory within extract_base_dir.

    Args:
        archive_path (str): Path to the downloaded USPTO archive file (.zip or .tar).
        extract_base_dir (str): The base directory where a unique extraction
                                 folder will be created.

    Returns:
        tuple: A tuple containing:
            - list: A list of full paths to the extracted XML files.
            - str: The path to the unique directory created for extraction.
                   Returns None if extraction fails.
    """
    extracted_xml_files = []
    unique_extract_dir = None
    archive_path_obj = Path(archive_path)
    file_extension = archive_path_obj.suffix.lower()

    try:
        # Create a unique directory for this archive's contents
        Path(extract_base_dir).mkdir(parents=True, exist_ok=True)
        unique_extract_dir = tempfile.mkdtemp(dir=extract_base_dir)
        logger.info(f"Created unique extraction directory: {unique_extract_dir}")

        # --- Handle PTGRDT (.tar) structure ---
        if file_extension == '.tar':
            logger.info(f"Processing PTGRDT tar file: {archive_path}")
            with tarfile.open(archive_path_obj, 'r') as tar_ref:
                for member in tar_ref.getmembers():
                    # Look for inner zip files within DESIGN or UTIL directories
                    if member.isfile() and \
                       (member.name.startswith('DESIGN/') or member.name.startswith('UTIL')) and \
                       member.name.lower().endswith('.zip'):

                        logger.debug(f"Found inner zip in tar: {member.name}")
                        inner_zip_path_in_tar = member.name
                        # Construct path where the inner zip will be temporarily extracted
                        inner_zip_extract_path = os.path.join(unique_extract_dir, os.path.basename(inner_zip_path_in_tar))

                        try:
                            # Extract the inner zip file itself first to the unique dir
                            # We need to extract it to disk to use zipfile on it reliably
                            member_fileobj = tar_ref.extractfile(member)
                            if member_fileobj: # Check if extractfile succeeded
                                with open(inner_zip_extract_path, 'wb') as f_out:
                                    shutil.copyfileobj(member_fileobj, f_out)
                                logger.debug(f"Extracted inner zip to: {inner_zip_extract_path}")
                            else:
                                logger.error(f"Failed to extract inner zip member {member.name} from tar.")
                                continue # Skip this inner zip

                            # Now open the extracted inner zip file
                            with zipfile.ZipFile(inner_zip_extract_path, 'r') as inner_zip_ref:
                                # Find the XML file within the inner zip
                                xml_files_in_inner = [
                                    f for f in inner_zip_ref.namelist() if f.lower().endswith('.xml')
                                ]
                                if not xml_files_in_inner:
                                    logger.warning(f"No XML file found in inner zip: {inner_zip_path_in_tar} (extracted to {inner_zip_extract_path})")
                                    continue
                                if len(xml_files_in_inner) > 1:
                                    logger.warning(f"Multiple XML files found in inner zip: {inner_zip_path_in_tar}. Extracting first: {xml_files_in_inner[0]}")

                                xml_member_name = xml_files_in_inner[0]
                                # Extract the XML file to the unique directory (same one)
                                extracted_path = inner_zip_ref.extract(xml_member_name, path=unique_extract_dir)
                                extracted_xml_files.append(extracted_path)
                                logger.debug(f"Extracted XML: {extracted_path} from inner zip {inner_zip_path_in_tar}")

                        except (zipfile.BadZipFile, Exception) as inner_err:
                            logger.error(f"Error processing inner zip {inner_zip_path_in_tar} from {archive_path}: {inner_err}", exc_info=True)
                        finally:
                             # Clean up the extracted inner zip file itself
                             if os.path.exists(inner_zip_extract_path):
                                 try:
                                     os.remove(inner_zip_extract_path)
                                     logger.debug(f"Cleaned up intermediate inner zip: {inner_zip_extract_path}")
                                 except OSError as del_err:
                                     logger.error(f"Failed to delete intermediate inner zip {inner_zip_extract_path}: {del_err}")

        # --- Handle CPCMCPT (.zip) structure ---
        elif file_extension == '.zip':
            logger.info(f"Processing CPCMCPT zip file: {archive_path}")
            with zipfile.ZipFile(archive_path_obj, 'r') as zip_ref:
                member_list = zip_ref.namelist()
                # Check for direct XML files (CPCMCPT structure)
                direct_xml_files = [
                    m for m in member_list if m.lower().endswith('.xml') # Allow XML in subdirs for CPC? Assuming yes for now.
                    # m for m in member_list if m.lower().endswith('.xml') and '/' not in m.strip('/') # Original stricter check
                ]
                if direct_xml_files:
                    logger.info(f"Detected {len(direct_xml_files)} direct XML file(s) in zip.")
                    for xml_member_name in direct_xml_files:
                        try:
                            extracted_path = zip_ref.extract(xml_member_name, path=unique_extract_dir)
                            extracted_xml_files.append(extracted_path)
                            logger.debug(f"Extracted direct XML: {extracted_path}")
                        except Exception as direct_err:
                             logger.error(f"Error extracting direct XML {xml_member_name} from {archive_path}: {direct_err}", exc_info=True)
                else:
                    # This case might happen if a .zip file follows the PTGRDT structure, which is unexpected.
                    # Or if it's an empty/invalid zip (though validation should catch most).
                    logger.warning(f"No direct XML files found in zip file: {archive_path}. Checking for PTGRDT-like structure (unexpected).")
                    # You could potentially re-add the PTGRDT inner-zip check here if needed,
                    # but the primary expectation is direct XMLs for .zip files.
                    inner_zip_paths = [
                        m for m in member_list
                        if (m.startswith('DESIGN/') or m.startswith('UTIL')) and m.lower().endswith('.zip')
                    ]
                    if inner_zip_paths:
                         logger.error(f"Unexpected PTGRDT structure (inner zips) found in a .zip file: {archive_path}. This is not handled.")
                    else:
                         logger.warning(f"No recognized patent XML structure found in {archive_path}")


        else:
            logger.error(f"Unsupported file type for extraction: {archive_path}. Only .zip and .tar are supported.")
            # Clean up the created directory if the file type is wrong
            if unique_extract_dir and os.path.exists(unique_extract_dir):
                 cleanup_extracted_directory(unique_extract_dir)
            return [], None


        if extracted_xml_files:
            logger.info(f"Successfully extracted {len(extracted_xml_files)} XML file(s) from {archive_path} to {unique_extract_dir}")
        else:
            logger.warning(f"No XML files were extracted from {archive_path}.")

        return extracted_xml_files, unique_extract_dir

    except (zipfile.BadZipFile, tarfile.ReadError, tarfile.TarError) as archive_err:
         logger.error(f"Failed to open or read archive file {archive_path}: {archive_err}", exc_info=True)
         if unique_extract_dir and os.path.exists(unique_extract_dir):
             cleanup_extracted_directory(unique_extract_dir)
         return [], None
    except Exception as e:
        logger.error(f"Error extracting patent archive file {archive_path}: {str(e)}", exc_info=True)
        if unique_extract_dir and os.path.exists(unique_extract_dir):
            cleanup_extracted_directory(unique_extract_dir) # Clean up on other errors too
        return [], None


def send_to_nas(local_path, remote_path):
    """
    Send a single file to NAS using NASConnection.

    Args:
        local_path (str): Local file path.
        remote_path (str): Remote path on NAS.

    Returns:
        bool: True if successful, False otherwise.
    """
    logger.info(f"Attempting to send file {local_path} to NAS at {remote_path}")
    try:
        with NASConnection() as nas:
            # Assuming the base remote directory exists and transfer_file_with_scp handles the rest.
            # remote_dir = os.path.dirname(remote_path) # Removed mkdir command
            
            nas.transfer_file_with_scp(local_path=local_path, remote_path=remote_path, to_nas=True)

        logger.info(f"Successfully sent file {local_path} to NAS at {remote_path}")
        return True

    except Exception as e:
        logger.error(f"Error sending file {local_path} to NAS at {remote_path}: {str(e)}", exc_info=True)
        return False

def send_folder_to_nas(local_folder, remote_folder):
    """
    Send an entire folder recursively to NAS using NASConnection.

    Args:
        local_folder (str): Local folder path.
        remote_folder (str): Remote base folder path on NAS. The contents
                             of local_folder will be placed inside remote_folder.

    Returns:
        bool: True if successful, False otherwise.
    """
    logger.info(f"Attempting to send folder {local_folder} to NAS at {remote_folder}")
    if not os.path.isdir(local_folder):
        logger.error(f"Local folder {local_folder} does not exist or is not a directory.")
        return False

    try:
        with NASConnection() as nas:
             # Assuming the base remote directory exists and ssh_local_to_nas handles the rest.
 
            # Use SCP's recursive option via ssh_local_to_nas or similar method
            # Assuming NASConnection.ssh_local_to_nas handles recursive transfer
            nas.ssh_local_to_nas(local_folder, remote_folder)

        logger.info(f"Successfully sent folder {local_folder} to NAS at {remote_folder}")
        return True

    except Exception as e:
        logger.error(f"Error sending folder {local_folder} to NAS at {remote_folder}: {str(e)}", exc_info=True)
        return False

def cleanup_extracted_directory(extract_dir):
    """
    Removes the temporary extraction directory.

    Args:
        extract_dir (str): The path to the directory created by extract_patent_zip.

    Returns:
        bool: True if successful, False otherwise.
    """
    logger.info(f"Attempting to clean up extraction directory: {extract_dir}")
    if not extract_dir or not os.path.exists(extract_dir):
        logger.warning(f"Cleanup skipped: Directory {extract_dir} is invalid or does not exist.")
        return False
    try:
        shutil.rmtree(extract_dir)
        logger.info(f"Successfully removed extraction directory: {extract_dir}")
        return True
    except OSError as e:
        logger.error(f"Error removing directory {extract_dir}: {str(e)}", exc_info=True)
        return False

# Example usage (optional, for testing)
if __name__ == '__main__':
    # This block is for testing purposes only and won't run when imported
    logger.info("Patent File Module - Test Execution")
    
    # Create dummy files/folders for testing if needed
    # test_url = "SOME_TEST_URL" # Replace with a real small zip URL for testing
    # test_zip_path = "temp_patent_download.zip"
    # test_extract_base = "temp_patent_extract"
    # test_nas_remote_file = "/path/on/nas/test_file.xml"
    # test_nas_remote_folder = "/path/on/nas/test_folder"

    # print("\n--- Testing Download ---")
    # if download_file(test_url, test_zip_path):
    #     print(f"Downloaded test file to {test_zip_path}")

    #     print("\n--- Testing Extraction ---")
    #     extracted_files, unique_dir = extract_patent_zip(test_zip_path, test_extract_base)
    #     if unique_dir:
    #         print(f"Extraction directory: {unique_dir}")
    #         print(f"Extracted XML files: {extracted_files}")

    #         if extracted_files:
    #             print("\n--- Testing Send File to NAS ---")
    #             # send_to_nas(extracted_files[0], test_nas_remote_file) # Requires NAS setup

    #             print("\n--- Testing Send Folder to NAS ---")
    #             # send_folder_to_nas(unique_dir, test_nas_remote_folder) # Requires NAS setup

    #         print("\n--- Testing Cleanup ---")
    #         cleanup_extracted_directory(unique_dir)

    #     # Clean up downloaded zip
    #     if os.path.exists(test_zip_path):
    #         os.remove(test_zip_path)
    # else:
    #     print(f"Failed to download test file from {test_url}")

    print("\n--- Patent File Module Loaded ---")