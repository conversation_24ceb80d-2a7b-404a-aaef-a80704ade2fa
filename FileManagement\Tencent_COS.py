## This way optimized by claude is 2x faster than the old way! No idea what it does :-P

from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import sys
# Removed: import logging
import os
sys.path.append(os.getcwd())
from logdata import log_message # Added logdata import
from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import sanitize_name
from qcloud_cos.cos_threadpool import SimpleThreadPool
import json
import zlib
import base64
import time
from Common.Constants import local_case_folder
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed

semaphore = None

def process_images_data(images_data):
    """Process images data from different formats"""
    if isinstance(images_data, str):
        return json.loads(images_data)
    return images_data if isinstance(images_data, dict) else {}


def get_case_dir(row):
    """Get case directory path"""
    return os.path.join(
        local_case_folder,
        sanitize_name(f"{row['date_filed'].strftime('%Y-%m-%d')} - {row['docket']}")
    )

def get_image_paths(images, case_dir, plaintiff_id):
    """Generate all image paths and keys"""
    for ip in images.keys():
        for image in images[ip].keys():
            paths = []
            # Regular images
            for res in ["high", "low"]:
                file_path = os.path.join(case_dir, "images", res, image)
                key = f'plaintiff_images/{int(plaintiff_id)}/{res}/{image}'
                paths.append((key, file_path))

            # Full images
            for full_image in images[ip][image]["full_filename"]:
                file_path = os.path.join(case_dir, "images", "high", full_image)
                key = f'plaintiff_images/{int(plaintiff_id)}/high/{full_image}'
                paths.append((key, file_path))

            yield image, paths



def get_cos_client():
    config = CosConfig(
        Region='ap-guangzhou',
        SecretId=os.getenv("COS_SECRET_ID"),
        SecretKey=os.getenv("COS_SECRET_KEY"),
        Token=None,
        Scheme='https'
    )

    return CosS3Client(config), 'troimages-1329604052'


### Used by Check
# What it is: A coroutine is a special type of function that can be paused during its execution and resumed later from where it left off. They are defined using the async def syntax in Python.
# Key features: Yielding control: When a coroutine encounters an await, it yields control back to the event loop, allowing the loop to run other coroutines.
async def async_upload_file_with_retry(client, bucket, key, file_path, max_retries=5):
    """Helper function for asynchronous file upload with retry logic"""
    # Removed logging manipulation block

    for attempt in range(max_retries):
        try:
            # Use a semaphore to limit concurrent connections
            # async with semaphore:
                loop = asyncio.get_event_loop()
                # The Tencent COS SDK (client.upload_file()) is synchronous/blocking code. To use it in an async context without blocking the event loop, we need to run it in a separate thread/process.
                response = await loop.run_in_executor(
                    None,  # Use default executor
                    lambda: client.upload_file(
                        Bucket=bucket,
                        LocalFilePath=file_path,
                        Key=key,
                        EnableMD5=False,
                        MAXThread=10
                    )
                )
                log_message(f"Uploaded 1 file to COS: {key}", level='INFO') # Added key for context
                return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            log_message(f"Failed to upload {key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
            await asyncio.sleep(2 ** attempt)  # Exponential backoff

    # Restore original logging configuration
    # Removed logging restoration block

### Used by Check
async def async_copy_file_with_retry(client, bucket, to_key, from_key, max_retries=3):
    """Helper function for asynchronous file upload with retry logic"""
    # Removed logging manipulation block

    for attempt in range(max_retries):
        try:
            # Use a semaphore to limit concurrent connections
            # async with semaphore:
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None,  # Use default executor
                    lambda: client.copy_object(
                        Bucket=bucket,
                        Key=to_key,
                        CopySource={
                            'Bucket': bucket,
                            'Key': from_key,
                            'Region': 'ap-guangzhou'
                        }
                    )
                )
                log_message(f"Copied {from_key} to {to_key} in COS", level='INFO') # Added keys for context
                return response
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            log_message(f"Failed to copy {from_key} to {to_key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
            await asyncio.sleep(2 ** attempt)  # Exponential backoff

    # Restore original logging configuration
    # Removed logging restoration block


def send_pictures_to_cos(df):
    """
    Synchronous version that uploads pictures to COS using ThreadPoolExecutor
    to process multiple files concurrently.
    """
    if len(df) == 0:
        return True

    client, bucket = get_cos_client()
    successful = 0
    failed = []
    upload_tasks = []

    # Collect all file paths and keys from the dataframe
    for index, row in df.iterrows():
        # Process images data
        images = process_images_data(row["images"])

        case_dir = get_case_dir(row)

        # Collect all file paths and keys
        for image, paths in get_image_paths(images, case_dir, row["plaintiff_id"]):
            upload_tasks.extend(paths)

    log_message(f"⬆️  ☁️ Uploading {len(upload_tasks)} files to COS for case {row['docket']}", level='INFO')

    # Configure logging to reduce noise
    # Removed logging manipulation block

    # Function to upload a single file with retry logic
    def upload_file_with_retry(key, file_path):
        max_retries = 5
        for attempt in range(max_retries):
            try:
                client.upload_file(
                    Bucket=bucket,
                    LocalFilePath=file_path,
                    Key=key,
                    EnableMD5=False,
                    MAXThread=1  # Single thread per file since files are small
                )
                # log_message(f"Uploaded file to COS: {key}", level='DEBUG') # Optional debug log
                return True, key
            except Exception as e:
                if attempt == max_retries - 1:
                    log_message(f"⬆️  ☁️ Failed to upload {key}: {str(e)}", level='ERROR')
                    return False, (key, str(e))
                else:
                    log_message(f"⬆️  ☁️ Failed to upload {key}: {str(e)}, retrying in {2 ** attempt} seconds", level='WARNING')
                    time.sleep(2 ** attempt)  # Exponential backoff with sleep

    if not upload_tasks:
        return True # Early return if no tasks to process

    max_workers = min(32, len(upload_tasks))  # Limit number of threads
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks to the executor
        future_to_key = {
            executor.submit(upload_file_with_retry, key, file_path): key
            for key, file_path in upload_tasks
        }

        # Process results as they complete
        for future in as_completed(future_to_key):
            result = future.result()
            if result[0]:  # Success
                successful += 1
            else:  # Failure
                failed.append(result[1])

    # Restore original logging configuration
    # Removed logging restoration block

    # Report results
    if failed:
        log_message(f"❌  ⬆️  ☁️ COS: {successful} files uploaded successfully, {len(failed)} failed, for case {row['docket']}", level='WARNING')
        log_message(f"Failed uploads: {failed}", level='WARNING')
        return False
    log_message(f"✅  ⬆️  ☁️ COS: All {successful} files uploaded successfully for case {row['docket']}", level='INFO')
    return True






def delete_cos_files(client, bucket, file_infos):
    # Construct the batch delete request.
    delete_list = []
    for file in file_infos:
        delete_list.append({"Key": file['Key']})

    response = client.delete_objects(Bucket=bucket, Delete={"Object": delete_list})
    log_message(f"COS Delete response: {response}", level='INFO') # Log response

def download_from_cos(client, bucket, cos_key, local_path):
    """Download a file from COS to a local path"""
    try:
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        response = client.download_file(
            Bucket=bucket,
            Key=cos_key,
            DestFilePath=local_path
        )
        return True
    except Exception as e:
        log_message(f"download_from_cos: Failed to download {cos_key}: {str(e)}", level='ERROR')
        return False



def list_all_files_in_COS(prefix=None):
    """
    Lists all files (keys) in the specified COS bucket that start with the given prefix.
    Handles pagination to retrieve more than 1000 files.
    """
    client, bucket = get_cos_client()
    all_files = []
    marker = ""
    while True:
        if prefix:
            response = client.list_objects(Bucket=bucket, Prefix=prefix, Marker=marker)
        else:
            response = client.list_objects(Bucket=bucket, Marker=marker)
        if 'Contents' in response:
            for content in response['Contents']:
                all_files.append(content['Key'])
        if response['IsTruncated'] == 'false':
            break
        marker = response["NextMarker"]
    return all_files


if __name__ == "__main__":
    import pandas as pd
    df = get_table_from_GZ("tb_case")
    df = df[df['date_filed'] == pd.to_datetime('2024-01-15').date()]
    # df = df[df['date_filed'] <= '2024-01-16']
    start_time = time.time()
    send_pictures_to_cos(df)
    end_time = time.time()
    print(f"Total time taken: {end_time - start_time:.1f} seconds")
