import os
import tempfile
from FileManagement.Tencent_COS import get_cos_client, download_from_cos
from Check.Data_Cache import get_cached_plaintiff_df, get_cached_cases_df
from logdata import log_check_message
from Check.Do_Check_Trademark import check_trademarks
from Check.Do_Check_Copyright import check_copyrights
from Check.Do_Check_Patent import check_patents
from langfuse.decorators import observe, langfuse_context
from AI.GC_VertexAI import vertex_genai_text_async
from multiprocessing import Pool
import threading
import time
import asyncio
import concurrent.futures
from Check.Do_Check_Download import download
import json
from FileManagement.Tencent_COS import async_upload_file_with_retry

async def download_and_check(check_id, client_name, main_product_image, other_product_images, ip_images, ip_keywords, description, reference_text, reference_images, **kwargs):
    print(f"📋 [CHECK:{check_id}] Starting download_and_check process")
    print(f"📋 [CHECK:{check_id}] client_name: {client_name}")
    print(f"📋 [CHECK:{check_id}] product images count: main={1 if main_product_image else 0}, other={len(other_product_images)}")
    print(f"📋 [CHECK:{check_id}] ip images count: {len(ip_images)}")
    print(f"📋 [CHECK:{check_id}] ip_keywords count: {len(ip_keywords)}")
    print(f"📋 [CHECK:{check_id}] description length: {len(description) if description else 0}")
    print(f"📋 [CHECK:{check_id}] reference_text length: {len(reference_text) if reference_text else 0}")
    print(f"📋 [CHECK:{check_id}] reference_images count: {len(reference_images)}")
    
    cos_client, cos_bucket = get_cos_client()
    # Create temporary directory for this check
    with tempfile.TemporaryDirectory() as temp_dir:
        start_time = time.time()
        print(f"📋 [CHECK:{check_id}] Starting file downloads")
        local_product_images, local_ip_images, local_reference_images = await download(cos_client, cos_bucket, temp_dir, check_id, main_product_image, other_product_images, ip_images, reference_images)
        download_time = time.time() - start_time
        print(f"🔨 [CHECK:{check_id}] Downloaded files in {download_time:.1f} seconds")

        start_time = time.time()
        try: 
            print(f"📋 [CHECK:{check_id}] Starting check process")
            # Pass through langfuse_parent_trace_id to ensure trace linking
            check_kwargs = {
                'cos_client': cos_client,
                'cos_bucket': cos_bucket,
                'temp_dir': temp_dir,
                'check_id': check_id,
                'client_name': client_name,
                'local_product_images': local_product_images,
                'local_ip_images': local_ip_images,
                'local_reference_images': local_reference_images,
                'description': description,
                'ip_keywords': ip_keywords,
                'reference_text': reference_text
            }
            
            # Add langfuse_parent_trace_id if it exists
            if 'langfuse_parent_trace_id' in kwargs:
                check_kwargs['langfuse_parent_trace_id'] = kwargs['langfuse_parent_trace_id']
                
            results = await check(**check_kwargs)
            check_time = time.time() - start_time
            print(f"🔨 [CHECK:{check_id}] Check completed in {check_time:.1f} seconds with results count: {len(results.get('results', []))}")
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            print(f"\033[91m[CHECK:{check_id}] CRITICAL ERROR IN CHECK: {str(e)}\nTRACEBACK:\n{tb}\033[0m")
            raise
        
        return results

@observe()
# @profile
async def check(cos_client, cos_bucket, temp_dir, check_id, client_name, local_product_images, local_ip_images, local_reference_images, description, ip_keywords, reference_text, **kwargs):
    # Use cached DataFrames if available
    start_time = time.time()
    plaintiff_df = get_cached_plaintiff_df()
    cases_df = get_cached_cases_df()

    log_check_message(f"Check started for check_id: {check_id}", check_id)
    print(f"🔨 [CHECK:{check_id}] Check: got dataframes (plaintiff_df & cases_df) in {time.time() - start_time:.1f} seconds")

    upload_tasks = []
    query_image_urls = {}
    for query_image_path in local_product_images+local_ip_images+local_reference_images:
        upload_tasks.append(asyncio.create_task(
            async_upload_file_with_retry(
                client=cos_client, bucket=cos_bucket,
                key=f"checks/{check_id}/query/{os.path.basename(query_image_path)}", 
                file_path=query_image_path
            )
        ))
        query_image_urls[os.path.basename(query_image_path)] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(query_image_path)}"

    start_time = time.time()
    print(f"📋 [CHECK:{check_id}] Starting IP checks (trademark, copyright, patent)")
    tasks = [check_trademarks(cos_client, cos_bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, description, ip_keywords, reference_text, plaintiff_df, query_image_urls),    
             check_copyrights(cos_client, cos_bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, plaintiff_df, query_image_urls), 
             check_patents(cos_client, cos_bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df, query_image_urls)
             ]
    all_ip_results = await asyncio.gather(*tasks)
    ip_check_time = time.time() - start_time
    
    results = [item for sublist in all_ip_results if sublist is not None for item in sublist]
    print(f"🔨 [CHECK:{check_id}] All tasks (trademark, copyright, patent) completed in {ip_check_time:.1f} seconds, with total raw results: {len(results)}")
    
    start_time = time.time()
    overall_risk_level = "低风险"
    for result in results:
        if result["risk_level"] == "中风险" and overall_risk_level == "低风险":
            overall_risk_level = "中风险"
        elif result["risk_level"] == "高风险" and (overall_risk_level == "低风险" or overall_risk_level == "中风险"):
            overall_risk_level = "高风险"
            break

    seen_keys = set()
    unique_results = []
    tasks = []
    task_indices = []
    
    for result in results:
        # Determine key type and validate
        if "ip_image" in result:
            current_key = (result["type"], result["ip_image"][0])
        else:
            # print(f"ip_owner for result: {result['ip_owner']} of type: {result['type']}")
            current_key = (result["type"], result["ip_owner"])

        # Common processing for both cases
        if current_key not in seen_keys:
            seen_keys.add(current_key)
            
            if "report" in result:
                result["report_en"] = result["report"]
                prompt = f"Translate this legal opinion report into chinese and preserve the original formatting. Report: \n\n {result['report']}"
                tasks.append(vertex_genai_text_async(prompt))
                task_indices.append(len(unique_results))
                
            if "plaintiff_id" in result:
                result["number_of_cases"] = cases_df[cases_df["plaintiff_id"] == int(result["plaintiff_id"])].shape[0]
                last_case = cases_df[cases_df["plaintiff_id"] == int(result["plaintiff_id"])].sort_values("date_filed", ascending=False).iloc[0]
                result["last_case_docket"] = last_case['docket']
                result["last_case_date_filed"] = str(last_case['date_filed'])

            unique_results.append(result)

    print(f"📋 [CHECK:{check_id}] Unique results: {len(unique_results)} => Starting translation of {len(tasks)} reports")
    
    responses = await asyncio.gather(*tasks)
    for i, response in enumerate(responses):
        unique_results[task_indices[i]]["report"] = response[response.find("*"):]

    translation_time = time.time() - start_time
    print(f"🔨 [CHECK:{check_id}] All translations completed in {translation_time:.1f} seconds")

    # Sort the results by risk level (High -> Medium -> Low) and then by type.
    def risk_and_type_sort_key(result):
        if result["risk_level"] == "高风险":
            risk_value = 0
        elif result["risk_level"] == "中风险":
            risk_value = 1
        else:  # "低风险"
            risk_value = 2
        return (risk_value, result["type"])

    unique_results.sort(key=risk_and_type_sort_key)

    for result in unique_results:
        if "query_image_path" in result:
            result.pop("query_image_path")
        if "internal_type" in result:
            result.pop("internal_type")
        if "ip_image" in result:
            for i in range(len(result["ip_image"])):
                result["ip_image"][i] = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/results/{result['ip_image'][i]}"

    # Create a separate copy of results for logging to avoid modifying the original data
    log_unique_results = []
    for result in unique_results:
        log_result = result.copy()  # Create a shallow copy of each dictionary
        if "report" in log_result:
            log_result["report"] = log_result["report"][:10]
        if "report_en" in log_result:
            log_result["report_en"] = log_result["report_en"][:10]
        log_unique_results.append(log_result)

    print(f'🔨 [CHECK:{check_id}] Check Analysis Completed with breakdown by risk level: {sum(1 for r in unique_results if r["risk_level"] == "高风险")} high, {sum(1 for r in unique_results if r["risk_level"] == "中风险")} medium, {sum(1 for r in unique_results if r["risk_level"] == "低风险")} low')

    await asyncio.gather(*upload_tasks)  # the upload of the original query images to the COS bucket
    print(f'📋 [CHECK:{check_id}] Uploaded {len(upload_tasks)} query images to COS')

    return {
        "check_id": check_id,
        "status": "success",
        "risk_level": overall_risk_level,
        "results": unique_results
    }