import os
import sys
import re
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add the project root directory to the Python path
from logdata import log_message
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch, insert_and_update_df_to_GZ_id
from AI.GC_VertexAI import vertex_genai_text, vertex_genai_text_async
from datetime import datetime
import pandas as pd
from langfuse.decorators import observe
import asyncio
import Common.Constants as Constants

# ❌⚠️📥🔥✅


## Search free limits:
# - Azure / Bing: 1000 queries / month, including it has images
# - Google: 100 queries / day
# - SerpAPI: 100 queries / month
# - undetected_chromedriver: ??? queries / hour

## LLM free limits: https://github.com/cheahjs/free-llm-api-resources
# - Gemini flash: 1500 queries / day & 15 per minutes
# - Gemini pro: 50 queries / day & 2 per minutes
# - Mistral (any model): 1 request/second
# - Google Cloud: experimental models are always free, 10 queries per minute, no daily cap: https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/gemini-experimental#console


def get_all_ai_summary_translations():
    cases_df = get_table_from_GZ("tb_case")
    get_ai_summary_translations(cases_df)
    insert_and_update_df_to_GZ_batch(cases_df, "tb_case", "id")

@observe()
def get_ai_summary_translations(df, indices=[]):
    log_message(f"🧠 Running Summary Translation...", level='INFO')
    for index, row in df.iterrows():
        if indices and index not in indices:
            continue
        if not row["aisummary"]:
            continue
        if "{" in row["aisummary"]:
            continue
        
        # Convert aisummary to string to handle potential float values safely
        summary_str = str(row["aisummary"]) if row["aisummary"] is not None else ""
        if summary_str and len(summary_str) > 10 and "{" not in summary_str:
            text = summary_str # Use the string version
            prompt = f'I will give you the overview of a legal case in English. I want you to provide me with the Chinese translation of the same case overview. You return your answer as a JSON that looks like this: {{"English": "The case overview in English", "Chinese": "The Chinese translation of the same case overview"}}.\n The English version of the Legal Case Overview is: "{text}"'
            # response = llm_call(prompt)
            response = vertex_genai_text(prompt, model_name=Constants.SMART_MODEL_FREE)
            df.loc[index, "aisummary"] = clean_ai_summary_translations_response(response)
            # print(f'{df.loc[index, "aisummary"]}')

    log_message(f"✅ Summary Translation completed.", level='INFO')


def clean_ai_summary_translations_response(response):
    if response:
        try:
            # Look for content between curly braces
            json_match = re.search(r'\{[^{}]*\}', response)
            if json_match:
                json_str = json_match.group(0)
                # Verify it has both English and Chinese keys
                if all(key in json_str for key in ["English", "Chinese"]):
                    return json_str
            
            # If no valid JSON found or missing required keys
            return '{"English":"Unknown", "Chinese": "未知"}'
        
        except Exception as e:
            print(f"Error parsing response: {e}")
            return '{"English":"Unknown", "Chinese": "未知"}'
    else:
        return '{"English":"Unknown", "Chinese": "未知"}'
    
@observe()
async def translate_steps(cases_steps_df):
    unique_case_ids = cases_steps_df["case_id"].unique()
    cases_steps_with_translation = pd.DataFrame()
    tasks = []
    semaphore = asyncio.Semaphore(10)
    
    # Create tasks with case_id tracking
    for case_id in unique_case_ids:
        # Pass case_id as a parameter to coroutine
        tasks.append(Constants.sem_task(semaphore, translate_steps_for_a_case(cases_steps_df, case_id)))
    

    # Get results in order with original case_ids
    translated_steps = await asyncio.gather(*tasks)
    
    # Pair results with original case_ids using zip
    for result, case_id in zip(translated_steps, unique_case_ids):
        if result is not None:
            cases_steps_with_translation = pd.concat([cases_steps_with_translation, result])
        else:
            # Use the case_id from our original list
            original_steps = cases_steps_df[cases_steps_df["case_id"] == case_id]
            cases_steps_with_translation = pd.concat([cases_steps_with_translation, original_steps])
    
    return cases_steps_with_translation


@observe()
async def translate_steps_for_a_case(cases_steps_df, case_id, batch_size=300):
    """
    Translates case proceeding steps from English to Chinese and returns a structured JSON
    Steps are naturally sorted (e.g., 3.1 comes before 3.10)
    """
    
    log_message(f"🧠 Running Steps Translation for case ID: {case_id}")
    
    case_steps = cases_steps_df[cases_steps_df["case_id"] == case_id]
    if "proceeding_text_cn" in case_steps.columns and all(case_steps["proceeding_text_cn"].notna()):
        return case_steps
    
    if case_steps.empty:
        log_message(f"No steps found for case ID: {case_id}")
        return None
    
    # Build steps dictionary
    steps_dict = {}
    for _, row in case_steps.iterrows():
        step_nb = str(row['step_nb'])  # Convert to string to ensure valid JSON key
        proceeding_text = str(row['proceeding_text']).strip()
        if proceeding_text:  # Only include non-empty proceedings
            steps_dict[step_nb] = proceeding_text
    
    if not steps_dict:
        print("No valid proceeding texts found")
        return case_steps
    
    # Create sorted dictionary and convert to JSON
    sorted_steps = dict(sorted(steps_dict.items(), key=lambda x: natural_sort_key(x[0])))

    translated_json = {}
    items = list(sorted_steps.items())
    for i in range(0, len(items), batch_size):
        batch = dict(items[i:i + batch_size])
        json_str = json.dumps(batch, ensure_ascii=False)

        # Prepare prompt for translation
        prompt = (
            "Translate the proceeding text in this legal case JSON from English to Chinese. "
            "Maintain the same JSON structure with step numbers as keys. "
            f"JSON to translate: {json_str}"
        )
    
        try:
            response = await vertex_genai_text_async(prompt, model_name=Constants.SMART_MODEL_FREE)
            if response:
                translated_json_new = extract_translation_json_from_text(response)

                if translated_json_new:
                    translated_json = {**translated_json, **translated_json_new}

        except Exception as e:
            print(f"Translation error: {e}")
            return case_steps

    # Check for missing or extra steps
    original_steps = set(steps_dict.keys())
    translated_steps = set(translated_json.keys())
    missing_steps = {float(step) for step in original_steps - translated_steps}
    extra_steps = {float(step) for step in translated_steps - original_steps}
    
    if missing_steps:
        if batch_size >= 2:
            new_batch_size = int(min(batch_size/2, len(case_steps)/2))
            print(f"⚠️ Steps trasnlation warning: Missing steps in translation after LLM call: {missing_steps}, try to decrease the batch size to {new_batch_size}")
            return await translate_steps_for_a_case(cases_steps_df, case_id, batch_size=new_batch_size)
        else:
            print(f"❌ Steps trasnlation failure:  Missing steps in translation after LLM call: {missing_steps}, giving up")
            return case_steps
    
    if extra_steps:
        print(f"❌ Steps trasnlation failure: Unexpected extra steps in translation: {extra_steps}, giving up")
        return case_steps
    
    # Update the DataFrame with translations
    for step_nb, chinese_text in translated_json.items():
        mask = (case_steps["case_id"] == case_id) & (case_steps["step_nb"].astype(str) == step_nb)
        case_steps.loc[mask, "proceeding_text_cn"] = chinese_text
    
    log_message(f"✅  Successfully updated translations Steps for case ID: {case_id}")
    return case_steps
            


def natural_sort_key(step_nb):
    """
    Convert step number (like '3.10') to a tuple of integers for natural sorting
    Example: '3.10' -> (3, 10), '3' -> (3, 0)
    """
    parts = str(step_nb).split('.')
    return tuple(int(part) for part in parts) + (0,) * (2 - len(parts))


def extract_translation_json_from_text(text):
    """Helper function to extract valid JSON from text"""
    try:
        # First try to find the outermost pair of curly braces
        start = text.find('{')
        end = text.rfind('}')
        if start != -1 and end == -1: 
            print(f"Warning: No closing brace found for JSON, going to add one")
           
            matches = list(re.finditer(r'"\d+\.\d{2}"', text))
            if matches:
                last_match = matches[-1]
                text = text[:last_match.start()]  # find the last step number
                text = text[:text.rfind('"')+1] + '}' # add the closing brace after the end of the last step text
                end = text.rfind('}')
            else:
                print(f"Warning: No step number found in the text")
                return {}
        if start != -1 and end != -1:
            json_str = text[start:end+1]
            return json.loads(json_str)
    except json.JSONDecodeError:
        print("Failed to parse JSON from response")
    return {}


if __name__ == "__main__":
    get_all_ai_summary_translations()

    # cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    # cases_df = cases_df[cases_df["id"] > 10000]
    # cases_steps_df = get_table_from_GZ("tb_case_steps", force_refresh=True)
    # for index, row in cases_df.iloc[::-1].iterrows():
    #     cases_steps = translate_steps_for_a_case(cases_steps_df, row["id"])
    #     if cases_steps is not None:
    #         insert_and_update_df_to_GZ_batch(cases_steps, "tb_case_steps", "id")

