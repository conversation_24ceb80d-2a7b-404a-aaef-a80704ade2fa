from datetime import datetime, timedelta
import time
import json
import zlib
import asyncio
import os
import pandas as pd
import copy
import sys
import io
from flask import Flask, render_template, request, jsonify
from concurrent.futures import ThreadPoolExecutor
from threading import Lock
from collections import OrderedDict
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch, get_gz_connection
from Check.Do_Check import download_and_check
from langfuse.client import Langfuse
import contextlib
import re

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Load API keys first to avoid circular imports
with open('api_keys.json', 'r') as f:
    allowed_api_keys = json.load(f)

# Create Flask app for standalone mode
standalone_app = Flask(__name__)

# Shared data structures for tracking tasks
executor = ThreadPoolExecutor(max_workers=4)
futures = OrderedDict()
queue_timestamps = {}
currently_processing = set()

api_key_usage = {}
api_key_lock = Lock()

# Initialize Langfuse client
try:
    langfuse = Langfuse()  # Assumes credentials are in environment variables
except Exception as e:
    print(f"Warning: Langfuse initialization failed: {e}")
    langfuse = None

# ANSI color to HTML conversion for better display in Langfuse
# def ansi_to_html(text):
#     """Convert ANSI color codes to more readable format with color circle emojis for Langfuse UI"""
#     # Define color mappings from ANSI to color circle emojis
#     # Format: ANSI code -> (start_emoji, end_emoji)
#     ansi_to_emoji = {
#         '\033[0m': '',  # Reset - no end emoji needed
#         '\033[30m': '⚫ ',  # Black
#         '\033[31m': '🔴 ',  # Red
#         '\033[32m': '🟢 ',  # Green
#         '\033[33m': '🟠 ',  # Orange
#         '\033[34m': '🔵 ',  # Blue
#         '\033[35m': '🟣 ',  # Purple
#         '\033[36m': '🟢🔵 ',  # Cyan (using green+blue)
#         '\033[37m': '⚪ ',  # White
#         '\033[90m': '⚫ ',  # Gray (using black)
#         '\033[91m': '🔴 ',  # Light Red
#         '\033[92m': '🟢 ',  # Light Green
#         '\033[93m': '🟡 ',  # Light Yellow
#         '\033[94m': '🔵 ',  # Light Blue
#         '\033[95m': '🟣 ',  # Light Purple
#         '\033[96m': '🟢🔵 ',  # Light Cyan (using green+blue)
#         '\033[97m': '⚪ ',  # Light White
#     }
    
#     # First convert escape code representation to actual escape codes
#     text = text.replace('[0m', '\033[0m')
#     text = text.replace('[30m', '\033[30m')
#     text = text.replace('[31m', '\033[31m')
#     text = text.replace('[32m', '\033[32m')
#     text = text.replace('[33m', '\033[33m')
#     text = text.replace('[34m', '\033[34m')
#     text = text.replace('[35m', '\033[35m')
#     text = text.replace('[36m', '\033[36m')
#     text = text.replace('[37m', '\033[37m')
#     text = text.replace('[90m', '\033[90m')
#     text = text.replace('[91m', '\033[91m')
#     text = text.replace('[92m', '\033[92m')
#     text = text.replace('[93m', '\033[93m')
#     text = text.replace('[94m', '\033[94m')
#     text = text.replace('[95m', '\033[95m')
#     text = text.replace('[96m', '\033[96m')
#     text = text.replace('[97m', '\033[97m')
    
#     # Keep track of active colors
#     current_color = None
#     result = []
    
#     # Split the text by ANSI codes
#     segments = re.split(r'(\x1B\[[0-9;]*m)', text)
    
#     for segment in segments:
#         if segment.startswith('\033['):
#             # This is a color code
#             if segment == '\033[0m':
#                 # Reset code
#                 current_color = None
#             else:
#                 # New color code
#                 current_color = segment
#                 result.append(ansi_to_emoji.get(segment, ''))
#         else:
#             # This is text content
#             if segment:  # Only append non-empty segments
#                 result.append(segment)
    
#     # Handle any remaining ANSI codes with a regex
#     ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
#     result_text = ''.join(result)
#     result_text = ansi_escape.sub('', result_text)
    
#     return result_text

def ansi_to_html(text):
    """Convert ANSI color codes to more readable format with color circle emojis for Langfuse UI"""
    # Define color mappings from ANSI to color circle emojis
    # Format: ANSI code -> (start_emoji, end_emoji)
    ansi_to_emoji = {
        '\033[0m': '',  # Reset - no end emoji needed
        '\033[30m': '⚫ ',  # Black
        '\033[31m': '🔴 ',  # Red
        '\033[32m': '🟢 ',  # Green
        '\033[33m': '🟠 ',  # Orange
        '\033[34m': '🔵 ',  # Blue
        '\033[35m': '🟣 ',  # Purple
        '\033[36m': '🟢🔵 ',  # Cyan (using green+blue)
        '\033[37m': '⚪ ',  # White
        '\033[90m': '⚫ ',  # Gray (using black)
        '\033[91m': '🔴 ',  # Light Red
        '\033[92m': '🟢 ',  # Light Green
        '\033[93m': '🟡 ',  # Light Yellow
        '\033[94m': '🔵 ',  # Light Blue
        '\033[95m': '🟣 ',  # Light Purple
        '\033[96m': '🟢🔵 ',  # Light Cyan (using green+blue)
        '\033[97m': '⚪ ',  # Light White
    }
    
    result = []
    
    # Split the text by ANSI codes
    segments = re.split(r'(\033\[[0-9;]*m)', text)
    
    for segment in segments:
        if segment.startswith('\033['):
            result.append(ansi_to_emoji.get(segment, ''))
        else:
            # This is text content
            result.append(segment)
    
    # Handle any remaining ANSI codes with a regex
    result_text = ''.join(result)
    
    return result_text



# Custom context manager to capture stdout while still printing to console
@contextlib.contextmanager
def capture_stdout():
    """Context manager to capture stdout and return it as a string while still printing to console."""
    captured_output = io.StringIO()
    original_stdout = sys.stdout
    
    # Create a custom stdout that writes to both the original stdout and our capture buffer
    class TeeOutput:
        def write(self, data):
            captured_output.write(data)
            original_stdout.write(data)
            
        def flush(self):
            captured_output.flush()
            original_stdout.flush()
    
    sys.stdout = TeeOutput()
    try:
        yield captured_output
    finally:
        sys.stdout = original_stdout

def refresh_api_keys():
    try:
        with api_key_lock:
            with open('api_keys.json', 'r') as f:
                new_keys = json.load(f)
            
            # Preserve existing usage data for keys that still exist
            global allowed_api_keys, api_key_usage
            allowed_api_keys = new_keys
            api_key_usage = {
                key: api_key_usage.get(key, {'last_reset': 0, 'count': 0, 'daily_count': 0, 'daily_window_start': 0})
                for key in new_keys
            }
            
        return jsonify({'status': 'success', 'keys_loaded': len(new_keys)})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    

# Function to check rate limit
def check_rate_limit(api_key):
    """Checks and updates rate limits with daily rolling window"""
    current_time = time.time()
    
    with api_key_lock:  # Acquire lock for thread safety
        usage = api_key_usage.get(api_key)
        config = allowed_api_keys.get(api_key, {})

        if not config:
            return False
        if not usage:
            usage = {'daily_count': 0, 'daily_window_start': current_time, 'last_reset': current_time, 'count': 0 }
            api_key_usage[api_key] = usage
        
        # Daily rolling window check
        if (current_time - usage['daily_window_start']) > 86400:
            usage['daily_count'] = 0
            usage['daily_window_start'] = current_time
            
        if usage['daily_count'] >= config.get('daily_limit', 100):
            return False

        # Minute-based rate limit
        if (current_time - usage['last_reset']) > 60:
            usage['last_reset'] = current_time
            usage['count'] = 0
            
        if usage['count'] >= config.get('rate_limit', 1):
            return False
        
        # Update counts
        usage['count'] += 1
        usage['daily_count'] += 1
        
    return True  # Lock released here when exiting 'with' block

def process_check_with_db(check_id, client_name, **kwargs):
    trace = None
    captured_output = None
    
    try:
        # Get the existing trace in Langfuse instead of creating a new one
        if langfuse: # This will reference the existing trace with this ID
            trace = langfuse.trace(id=str(check_id), metadata={ "processing_started": datetime.now().isoformat() })
        
        # Capture all stdout during the entire execution
        with capture_stdout() as captured_output:
            # Run the analysis
            print(f"📥 Starting download_and_check for check_id: {check_id}")
            # Pass the trace_id as langfuse_parent_trace_id for proper linking
            kwargs["langfuse_parent_trace_id"] = str(check_id)
             
            json_results = asyncio.run(download_and_check(check_id=check_id, client_name=client_name, **kwargs))
            
            # Store result in database
            print(f"📤 process_check_with_db: Storing result in database for check_id: {check_id}")
            
            db_result_data = {
                'check_id': int(check_id),
                'check_date': datetime.now().date(),
                'result': json.dumps(json_results)  # Serialize JSON results to string
            }
            db_result_df = pd.DataFrame([db_result_data])

            if client_name == "MiniApp" or client_name == "H5":
                insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result', 'check_id')
            elif client_name == "MiniAppDev" or client_name == "H5Dev":
                connection = get_gz_connection(host="maidalv.com", port=3307)
                insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result', 'check_id', conn=connection)
            else:
                insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result_api', 'check_id')
            
            return json_results
    except Exception as e:
        # Use app logger if running in the main app, otherwise use print
        error_msg = f"Error processing check {check_id}: {str(e)}"
        print(f"🔥 {error_msg}")
        
        # Update trace status but don't end it yet
        if trace:
            trace.update(metadata={ "error": f"🔥🔥🔥 {error_msg}" }, status="error")
        
        raise
    finally:
        # Get captured logs if available
        if captured_output:
            log_output = captured_output.getvalue()
            
            # Send logs to Langfuse
            if trace:
                trace.update(metadata={ "logs": ansi_to_html(log_output) }, status="success")
        
        # Close the trace - replace end() with update status
        if trace:
            trace.update(status="success")
        
        if 'connection' in locals():
            connection.close()

def api_studio():
    return render_template('api_studio.html')

def check_status(check_id):
    # Check if task is being tracked in futures
    is_tracked = check_id in futures
    
    # If task is tracked and still processing
    if is_tracked and not futures[check_id].done():
        # Calculate queue position based on submission time
        queue_position = 1  # Start at 1 (this task itself)
        
        # If this task is being processed, it's at position 0
        if check_id in currently_processing:
            queue_position = 0
        else:
            # Count how many tasks were submitted before this one and are still pending
            current_time = queue_timestamps.get(check_id, time.time())
            for task_id, timestamp in queue_timestamps.items():
                if task_id != check_id and timestamp < current_time and not futures[task_id].done():
                    queue_position += 1
        
        # Estimate time based on queue position
        estimated_time = max(1, queue_position)  # At least 1 minute
        
        return jsonify({
            'status': 'processing',
            'queue_position': queue_position,
            'estimated_completion_time': f'{estimated_time} minutes'
        })
    
    # For completed tasks or unknown tasks, check the database
    print(f"Checking database for results for check_id: {check_id}")
    connection = get_gz_connection()
    try:
        # Use a cursor and fetchall, similar to ImportExport.py
        cursor = connection.cursor()
        query = f"SELECT result FROM tb_case_check_result_api WHERE check_id = %s"  # Use parameterized query
        cursor.execute(query, (check_id,))  # Pass check_id as a parameter
        data = cursor.fetchall()

        if data:  # Check if data was returned (fetchall returns an empty list if no rows match)
            print(f"✅✅✅ Results are sent to client for check_id: {check_id}")
            # Extract the 'result' from the first row (data[0]) and first column (data[0][0])
            result_json = data[0][0]
            
            # If this task is still in futures, clean it up
            if is_tracked and futures[check_id].done():
                del futures[check_id]
                if check_id in queue_timestamps:
                    del queue_timestamps[check_id]
            
            # Update the Langfuse trace with result info when client receives the result
            if langfuse:
                try:
                    # Get the existing trace
                    trace = langfuse.trace(
                        id=str(check_id),
                        metadata={ "✅✅✅ Results are sent to client at": datetime.now().isoformat() }
                    )
                    
                    print(f"📊 Updated Langfuse trace with result info for check_id: {check_id}")
                except Exception as trace_error:
                    print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}: {str(trace_error)}")
                
            return jsonify({
                'status': 'completed',
                'result': json.loads(result_json)
            })
        else:
            print(f"📥 No results found in database for check_id: {check_id}")
            error_message = f"Results not found for check_id: {check_id}"
            
            # Update Langfuse trace with error info
            if langfuse:
                try:
                    trace = langfuse.trace(
                        id=str(check_id),
                        metadata={ "error in check_status": error_message, "error_code": "RESULTS_NOT_FOUND => 404" }
                    )
                    print(f"📊 Updated Langfuse trace with error info for check_id: {check_id}")
                except Exception as trace_error:
                    print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}: {str(trace_error)}")
            
            return jsonify({
                'status': 'error', 
                'message': error_message,
                'error_code': 'RESULTS_NOT_FOUND'
            }), 404

    except Exception as e:
        error_message = f"Database error while retrieving results: {str(e)}"
        print(f"🔥 Error in check_status: {error_message}")
        
        # Update Langfuse trace with database error info
        if langfuse:
            try:
                trace = langfuse.trace(
                    id=str(check_id),
                    metadata={"error": error_message, "error_code": "DATABASE_ERROR => 500", "details": str(e)}
                )
                print(f"📊 Updated Langfuse trace with database error info for check_id: {check_id}")
            except Exception as trace_error:
                print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}: {str(trace_error)}")
        
        return jsonify({
            'status': 'error', 
            'message': error_message,
            'error_code': 'DATABASE_ERROR',
            'details': str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        connection.close()  # Ensure connection is closed


def check_api():
    data = request.get_json()
    
    # Create a copy of data for logging to avoid modifying the original
    log_data = copy.deepcopy(data) if data else {}
    
    # Identify and truncate likely base64 image strings for logging
    for key, value in log_data.items() if isinstance(log_data, dict) else []:
        if key in ['main_product_image', 'other_product_images', 'ip_images', 'reference_images']:
            # Handle array of images or direct image strings
            if isinstance(value, list):
                for i in range(len(value)):
                    if isinstance(value[i], str) and len(value[i]) > 500:
                        log_data[key][i] = f"[base64 image: {len(value[i])} chars]"
            elif isinstance(value, str) and len(value) > 500:
                log_data[key] = f"[base64 image: {len(value)} chars]"

    print(f"\n\n📥📥📥 API request received at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Parsed JSON data: {log_data}")
    
    try:
        start_time = time.time()
        # Retrieve and validate API key first
        api_key = data.get('api_key')
        if not api_key or api_key not in allowed_api_keys:
            error_message = "Invalid API Key. Authentication failed."
            print(f"⛔ API authentication failed for {api_key}: {error_message}")
            return jsonify({
                'error': error_message, 
                'status': 'failed',
                'error_code': 'AUTH_FAILED'
            }), 401
        else:
            client_info = allowed_api_keys.get(api_key)
            
        # Check rate limit before processing
        if not check_rate_limit(api_key):
            rate_limits = f"max {client_info['rate_limit']}/min and {client_info['daily_limit']}/day"
            error_message = f"Rate limit exceeded - {rate_limits}"
            print(f"⛔ API rate limit exceeded for {api_key}: {rate_limits}")
            return jsonify({
                'error': error_message,
                'status': 'failed',
                'error_code': 'RATE_LIMIT_EXCEEDED',
                'rate_limits': {
                    'per_minute': client_info['rate_limit'],
                    'per_day': client_info['daily_limit']
                }
            }), 429

        # Generate the check ID using the global function (but if sent from miniapp or website the check_id will be sent from the frontend)
        if data.get('check_id', '') == '':
            now = datetime.now()
            check_id = f"{client_info['id']}{now.strftime('%Y%m%d%H%M%S')}"
            print(f"Generated check_id: {check_id}")
        else:
            check_id = data.get('check_id')
            print(f"Using provided check_id: {check_id}")

        # Create a trace in Langfuse
        trace = None

        if langfuse:
            trace = langfuse.trace(
                name=f"check",
                id=str(check_id),
                user_id=client_info['client_name'],
                input=log_data,
                metadata={ 
                    "check_id": check_id,
                    "client": client_info['client_name'],
                    "api_key": api_key
                }
            )
            
            # Use with statement for capturing stdout
            with capture_stdout() as captured_output:
                # Retrieve and validate other required fields.
                product_category = data.get('product_category', '') or ""
                main_product_image = data.get('main_product_image')
                if not main_product_image:
                    error_message = "Missing required field: main_product_image"
                    print(f"⛔ API validation error: {error_message}")
                    
                    # Get logs before returning
                    log_output = captured_output.getvalue()
                    trace.update(metadata={"check_api_logs": ansi_to_html(log_output), "error": error_message}, status="error")
                    
                    return jsonify({
                        'error': error_message,
                        'status': 'failed',
                        'error_code': 'MISSING_REQUIRED_FIELD'
                    }), 400
                    
                other_product_images = data.get('other_product_images', []) or []
                ip_images = data.get('ip_images', []) or []
                ip_keywords = data.get('ip_keywords', []) or []
                description = data.get('description', '')
                    
                reference_text = data.get('reference_text', '') or ""
                reference_images = data.get('reference_images', []) or []
                
                # Create case check record for the database
                db_case_check_data = {
                    'id': int(check_id),
                    'user_id': f"{client_info['client_name']}_{api_key}",
                    'product_category': product_category,
                    'images': zlib.compress(json.dumps(ip_images).encode()).hex(),  # Serialize list to JSON string
                    'keyword': json.dumps(ip_keywords),  # Serialize list to JSON string
                    'product_images': zlib.compress(main_product_image.encode()).hex(),
                    'other_product_images': zlib.compress(json.dumps(other_product_images).encode()).hex(),  # Serialize list
                    'product_describe': description,
                    'reference_source': reference_text,
                    'reference_images': zlib.compress(json.dumps(reference_images).encode()).hex()  # Serialize list
                }
                db_case_check_df = pd.DataFrame([db_case_check_data])

                if client_info['client_name'] != "MiniApp" and client_info['client_name'] != "H5" and client_info['client_name'] != "MiniAppDev" and client_info['client_name'] != "H5Dev":
                    insert_and_update_df_to_GZ_batch(db_case_check_df, 'tb_case_check_api', 'id')

                end_time = time.time()
                print(f"📤 Check_API : Inserted check_id: {check_id} in Database in {end_time - start_time} seconds")

    except KeyError as e:
        field_name = str(e).strip("'")
        error_message = f"Missing required field: {field_name}"
        print(f"⛔ API validation error: {error_message}")
        
        # Create or update trace with error if available
        if 'trace' in locals() and trace:
            if 'captured_output' in locals() and captured_output:
                log_output = captured_output.getvalue()
                trace.update(metadata={"check_api_logs": ansi_to_html(log_output), "Check_API KeyError": error_message}, status="error")
            else:
                trace.update(metadata={"Check_API KeyError": error_message}, status="error")
            
        return jsonify({
            'error': error_message,
            'status': 'failed',
            'error_code': 'MISSING_REQUIRED_FIELD'
        }), 400
    except Exception as e:
        error_message = f"Internal server error: {str(e)}"
        print(f"🔥 API server error: {error_message}")
        
        # Create or update trace with error if available
        if 'trace' in locals() and trace:
            if 'captured_output' in locals() and captured_output:
                log_output = captured_output.getvalue()
                trace.update(metadata={"check_api_logs": ansi_to_html(log_output), "Check_API SERVER_ERROR": error_message}, status="error")
            else:
                trace.update(metadata={"Check_API SERVER_ERROR": error_message}, status="error")
            
        return jsonify({
            'error': error_message,
            'status': 'failed',
            'error_code': 'SERVER_ERROR',
            'details': str(e)
        }), 500

    try:
        # Submit to executor with proper error handling
        future = executor.submit(
            process_check_with_db,
            check_id=check_id,
            client_name=client_info['client_name'],
            main_product_image=main_product_image,
            other_product_images=other_product_images,
            ip_images=ip_images,
            ip_keywords=ip_keywords,
            description=description,
            reference_text=reference_text,
            reference_images=reference_images
        )
        
        # Add a callback to track when tasks start and finish processing
        def task_state_callback(future):
            if not future.done():
                currently_processing.add(check_id)
            else:
                currently_processing.discard(check_id)
                # Optionally clean up the timestamp when done
                if check_id in queue_timestamps:
                    del queue_timestamps[check_id]
        
        future.add_done_callback(task_state_callback)

        # Add the future to the futures dictionary, keyed by check_id
        futures[check_id] = future
        queue_timestamps[check_id] = time.time()  # Record submission time

        # Calculate queue position (add 1 because it includes the currently running task)
        queue_position = sum(1 for f in futures.values() if not f.done())

        # Add 1 to include any currently running tasks.
        queue_position += len(executor._threads)
        
        # Calculate estimated time (assuming 1/3 minute per task) and round up to nearest integer
        estimated_time = int(queue_position * 0.33 + 0.9999)

        # Get logs from the captured output if available
        if 'captured_output' in locals() and captured_output:
            log_output = captured_output.getvalue()
            
            # Send logs to Langfuse
            if trace:
                # Update trace with logs but don't end it yet
                trace.update(metadata={ "check_api_logs": ansi_to_html(log_output) }, status="success")

        return jsonify({
            'check_id': check_id,
            'status': 'processing',
            'message': 'Analysis started. Use check_status endpoint to poll results.',
            'estimated_completion_time': f"{estimated_time} minutes"
        })
    except Exception as e:
        error_message = f"Error starting analysis: {str(e)}"
        print(f"🔥 API processing error: {error_message}")
        
        # Update trace with error if available
        if trace:
            if 'captured_output' in locals() and captured_output:
                log_output = captured_output.getvalue()
                trace.update(metadata={ "check_api_logs": ansi_to_html(log_output), "error": error_message}, status="error")
            else:
                trace.update(metadata={"error": error_message}, status="error")
        
        return jsonify({
            'error': error_message, 
            'status': 'failed',
            'error_code': 'PROCESSING_ERROR',
            'details': str(e)
        }), 500

# Define routes for standalone mode
@standalone_app.route('/api_studio')
def api_studio_route():
    return api_studio()

@standalone_app.route('/check_status/<check_id>')
def check_status_route(check_id):
    return check_status(check_id)

@standalone_app.route('/check_api', methods=['POST'])
def check_api_route():
    return check_api()

# Standalone server
if __name__ == '__main__':
    import os
    from waitress import serve

    from Check.RAG.RAG_Inference import load_all_models
    load_all_models() 

    from Check.Data_Cache import update_dataframe_cache
    update_dataframe_cache()
    
    print("Starting API Studio Server...")
    serve(
        standalone_app,
        host="0.0.0.0",
        port=5000,
        threads=10,  # The number of requests Waitress can actively process at the same time. This is your primary concurrency control.
        connection_limit=40,  # The maximum number of connections Waitress will accept, including those being actively processed and those waiting in its internal queue.
        channel_timeout=900
    )
