"""
Normalization service for normalizing similarity scores.
"""

from typing import List, Dict, Any, <PERSON>, Tu<PERSON>

def normalize_copyright_clip_score(score: float) -> float:
    """
    Normalize a CLIP similarity score for copyright.
    In the original RAG_Copyright.py, CLIP scores are normalized where 0.75 becomes 1.0.
    
    Args:
        score: The raw similarity score.
        
    Returns:
        The normalized score.
    """
    # Normalize where 0.75 becomes 1.0
    return score / 0.75

def normalize_copyright_efficientnet_score(score: float) -> float:
    """
    Normalize an EfficientNet similarity score for copyright.
    In the original RAG_Copyright.py, EfficientNet scores are normalized where 0.6 becomes 1.0.
    
    Args:
        score: The raw similarity score.
        
    Returns:
        The normalized score.
    """
    # Normalize where 0.6 becomes 1.0
    return score / 0.6

def calculate_copyright_combined_score(clip_score: float, efficientnet_score: float) -> float:
    """
    Calculate a combined score from CLIP and EfficientNet scores for copyright.
    
    Args:
        clip_score: The raw CLIP similarity score.
        efficientnet_score: The raw EfficientNet similarity score.
        
    Returns:
        The combined normalized score.
    """
    # Normalize individual scores
    normalized_clip = normalize_copyright_clip_score(clip_score)
    normalized_efficientnet = normalize_copyright_efficientnet_score(efficientnet_score)
    
    # Calculate combined score (average of normalized scores)
    combined_score = (normalized_clip + normalized_efficientnet) / 2
    
    return combined_score

def normalize_patent_image_score(score: float) -> float:
    """
    Normalize an image similarity score for patent.
    In the original RAG_Patent.py, image scores use a threshold of 0.6.
    
    Args:
        score: The raw similarity score.
        
    Returns:
        The normalized score.
    """
    # For patents, we don't apply specific normalization, just return the raw score
    # The threshold is applied separately
    return score

def normalize_patent_text_score(score: float) -> float:
    """
    Normalize a text similarity score for patent.
    In the original RAG_Patent.py, text scores use a threshold of 0.25.
    
    Args:
        score: The raw similarity score.
        
    Returns:
        The normalized score.
    """
    # For patents, we don't apply specific normalization, just return the raw score
    # The threshold is applied separately
    return score

def normalize_trademark_score(score: float) -> float:
    """
    Normalize a similarity score for trademark.
    In the original RAG_Trademark.py, EfficientNet scores are used with a complex matching logic.
    
    Args:
        score: The raw similarity score.
        
    Returns:
        The normalized score.
    """
    # Normalize where 0.6 becomes 1.0, similar to copyright EfficientNet
    return score / 0.6

def combine_results_by_id(results_list: List[List[Dict[str, Any]]]) -> Dict[str, Dict[str, Any]]:
    """
    Combine results from different approaches by ID.
    
    Args:
        results_list: A list of result lists from different approaches.
        
    Returns:
        A dictionary mapping ID to combined result information.
    """
    combined_results = {}
    
    # Process each result list
    for results in results_list:
        for result in results:
            result_id = result.get("id", "")
            if not result_id:
                continue
                
            # Initialize if not exists
            if result_id not in combined_results:
                combined_results[result_id] = {
                    "id": result_id,
                    "approaches": {},
                    "max_score": 0.0,
                    "combined_score": 0.0
                }
            
            # Add approach-specific information
            approach = result.get("approach", "unknown")
            score = result.get("score", 0.0)
            
            combined_results[result_id]["approaches"][approach] = {
                "score": score,
                "normalized_score": 0.0  # Will be set later
            }
            
            # Update max score if higher
            if score > combined_results[result_id]["max_score"]:
                combined_results[result_id]["max_score"] = score
    
    return combined_results

def normalize_and_combine_copyright_results(clip_results: List[Dict[str, Any]], 
                                          efficientnet_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Normalize and combine copyright results from different approaches.
    
    Args:
        clip_results: Results from CLIP approach.
        efficientnet_results: Results from EfficientNet approach.
        
    Returns:
        A list of combined results with normalized scores.
    """
    # Combine results by ID
    combined_dict = combine_results_by_id([clip_results, efficientnet_results])
    
    # Process each combined result
    for result_id, result_info in combined_dict.items():
        # Get approach-specific scores
        clip_score = result_info["approaches"].get("clipv2", {}).get("score", 0.0)
        efficientnet_score = result_info["approaches"].get("b7", {}).get("score", 0.0)
        
        # Normalize scores
        if "clipv2" in result_info["approaches"]:
            result_info["approaches"]["clipv2"]["normalized_score"] = normalize_copyright_clip_score(clip_score)
            
        if "b7" in result_info["approaches"]:
            result_info["approaches"]["b7"]["normalized_score"] = normalize_copyright_efficientnet_score(efficientnet_score)
        
        # Calculate combined score if both approaches are present
        if "clipv2" in result_info["approaches"] and "b7" in result_info["approaches"]:
            result_info["combined_score"] = calculate_copyright_combined_score(clip_score, efficientnet_score)
            result_info["approaches"]["combined"] = {
                "score": result_info["combined_score"],
                "normalized_score": result_info["combined_score"]  # Already normalized
            }
        # If only one approach is present, use its normalized score
        elif "clipv2" in result_info["approaches"]:
            result_info["combined_score"] = result_info["approaches"]["clipv2"]["normalized_score"]
        elif "b7" in result_info["approaches"]:
            result_info["combined_score"] = result_info["approaches"]["b7"]["normalized_score"]
    
    # Convert dictionary to list and sort by combined score
    combined_list = list(combined_dict.values())
    combined_list.sort(key=lambda x: x["combined_score"], reverse=True)
    
    return combined_list
