import os
import json
import asyncio
import re  # Import the 're' module
from dotenv import load_dotenv
import google.generativeai as genai
from AI.GC_VertexAI import vertex_genai_multi_async, parse_json
from AI.LLM_shared import get_json
import pandas as pd
from AI.GoogleSearch import search_and_save_images #Import GoogleSearch
import shutil # Import shutil for file operations
from Alerts.Plaintiff import remove_acronyms
from langfuse.decorators import observe, langfuse_context
from logdata import log_message
from Common.Constants import sanitize_name  # Import sanitize_name function



# Get environment variables (combined into single line checks)
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)


def clean_claimant(claimant):
    """Cleans up the copyright claimant string for better search queries."""
    claimant = re.sub(r"Transfer: By written agreement\..*", "", claimant, flags=re.IGNORECASE)
    claimant = re.sub(r"Address:.*", "", claimant, flags=re.IGNORECASE)
    claimant = claimant.replace(",", "").replace(".", "")
    claimant = remove_acronyms(claimant)
    claimant = claimant.strip()
    return claimant


async def select_best_copyright_image_from_search_results(search_query, image_data_list, claimant, title, basis_of_claim, nation_of_publication):

    basis_of_claim_prompt =  f'*   **Basis of Claim:** "{basis_of_claim}"' if basis_of_claim else ""
    nation_of_publication_prompt =  f'*   **Nation of First Publication:** "{nation_of_publication}"' if nation_of_publication else ""

    #Uses Vertex GenAI to analyze screenshots and categorize image types.
    prompt_list = [("text", f"""You are a highly experienced copyright image analyst tasked with identifying the best match for a registered artwork from a set of Google Images search results.Your primary goal is to ensure that the match is accurate, preferring a "no match" or "low confidence" outcome over an incorrect positive match.

**Contextual Information:**

*   **Search Query:** "{search_query}" (This was the original search query used on Google Images, combining the Copyright Claimant and Title).
*   **Copyright Claimant:** "{claimant}"
*   **Title:** "{title}"
{basis_of_claim_prompt}
{nation_of_publication_prompt}

**Keyword Analysis:**

*   **Claimant Keywords:** (Analyze the `claimant` string and extract keywords. Consider removing common words like "Inc.", "LLC", "Ltd.", etc. Prioritize unique names or identifiers.)
*   **Title Keywords:** (Analyze the `title` string and extract keywords. Identify potentially unique or distinguishing words/phrases within the title. For example, in "GUANGDONG_ZHONGKANG_EMBROIDERY_TECHNOLOGY_COLTD_Pattern_800559-130", "ZHONGKANG" and "800559-130" are likely more important than "GUANGDONG", "EMBROIDERY", "TECHNOLOGY", "COLTD", or "Pattern".)
    *   **Title Specificity Assessment:** Assess the specificity of the title. Is it a descriptive title (e.g., "Sunset over Mountains") or a generic/identifier-based title (e.g., "POSTER 27", "Pattern 800559-130")? If the title is generic or primarily an identifier, note that this reduces the reliability of the title for matching purposes.  *Specifically, consider if the title includes both descriptive elements AND identifier codes/numbers.  The presence of an descriptive elements alone does not guarantee a match unless that identifier is also present in the search result's title or image content.*
*Crucially, analyze the Title, HTML Title, Link, and Display Link attached to each searched image. Compare these elements with the claimant and title of the registered artwork to identify clues and assess the probability of a match.*
**Image Analysis Task:**

Verify the key textual elements (identifier code, claimant name, etc.) match exactly with the registered details; assess whether the visual style and content are congruent with the registered artwork; assign high confidence only if both textual and visual analyses achieve ≥90% match; if any primary element is missing or conflicting, reduce confidence significantly or mark as "no match."  Additionally, for identifier-only titles, if the identifier does not appear in the title or image content, mark the result as low probability.
*The registered artwork should be a single, distinct, and standalone piece. It must not consist of multiple artworks combined, collaged, or presented together in a polished manner, such as in a website layout or promotional material. The AI should lower the probability of generating or identifying combined artworks as a single copyrighted work. The artwork should be unique in composition, style, and execution, with clear, individual artistic intent and authorship.
Image Categories:

1.  Clear Artwork: A clean, unobstructed depiction of the artwork itself.
2.  Artwork on Product: The artwork applied to a product (book cover, merchandise, etc.).
3.  Artwork in Context (Non-Product): Artwork in a setting, but not for sale.
4.  Video/Screenshot/Frame: A still frame from a video.
5.  Other (Rare/Unclear): Doesn't fit into the above categories.

**Assumptions and Considerations:**

*   **Language and Discoverability:** If the claimant name appears to be Chinese (based on common Chinese characters or naming conventions), there's a higher likelihood that the registered artwork may be less easily discoverable or verifiable through a standard Google Images search.  This is due to potential differences in online presence and indexing between Western and Chinese platforms.  Therefore, exercise increased caution when evaluating matches for potentially Chinese-named claimants.  *However*, if the information extracted from an image (e.g., text on the image, accompanying descriptions) shows a very high degree of overlap with the provided copyright details (claimant, title, keywords), this can override the initial assumption of lower discoverability.* Give higher weight to images with strong textual or contextual matches in such cases.
*   **Title Specificity and Match Confidence:** If the 'Title Specificity Assessment' determined the title to be generic or identifier-based, significantly reduce confidence (probability) assigned to matches; even with descriptive elements, an unmatched identifier code/number (not in the Google Images result title or image content) reduces probability; assign high confidence only if: (a) the title is highly descriptive AND the image visually matches; (b) the title has an identifier code/number AND that *exact* code/number is visible in the search result (title text or image); (c) strong corroborating evidence *beyond the title* exists (e.g., claimant's name on image, visual elements matching 'Basis of Claim').
* **Multiple Artworks by Same Claimant:** Be aware that a single claimant may register multiple artworks.  A generic or identifier-based title, without further corroborating evidence, is insufficient to establish a match, as it could apply to any of the claimant's registered works.
""")]
    
    image_url = [""]

    valid_image_data_list = []
    for idx, image_data in enumerate(image_data_list, start=1):
        image_path = image_data[0]
        metadata = image_data[1]

        # Check if the image file exists and is a valid file
        if not os.path.isfile(image_path):
            print(f"[ERROR] Image file not found: {image_path}")
            continue  # Skip to the next image
        
        if os.path.getsize(image_path) == 0:
            print(f"[ERROR] Image file is empty: {image_path}")
            continue  # Skip to the next image
        
        valid_image_data_list.append(image_data)

    for idx, image_data in enumerate(valid_image_data_list, start=1):
        image_path = image_data[0]
        metadata = image_data[1]

        prompt_list.append(("text", f"\n\nResult {idx}:\nTitle: {metadata.get('title', '')}\nHTML Title: {metadata.get('htmlTitle', '')}\nLink: {metadata.get('link', '')}\nDisplay Link: {metadata.get('displayLink', '')}\n"))
        image_url.append("")
        prompt_list.append(("image_path", image_path))
        image_url.append(metadata.get('link', ''))

    prompt_list.append(("text", (
       "Please analyze these results, considering the contextual information and keyword analysis provided above. Identify the 3 images that best match the registered artwork, *and* categorize each image according to the categories above.\n"
        'Return your answer as follow: {"Result ***": {"match": "0.xx", "category": "Y"}, "Result ***": {"match": "0.xx", "category": "Y"}, "Result ***": {"match": "0.xx", "category": "Y"}} where 0.xx is your assessment of the probability that the picture is the registered artwork (0.00 to 1.00), and Y is the category index (1-5).  Higher probabilities indicate a stronger match.\n'
        "Separately, also provide the category of the image you ranked as the best match, in the format: 'Best Match Category: X', where X is the category name.\n"
        "Finally, provide a concise summary (one paragraph) of your analysis and reasoning for selecting the best matches, starting with the phrase 'Explanation Summary: ', and specifically addressing: the reasoning for the best match choice, overall confidence levels (probabilities) of the top matches (e.g., high, low, or mixed), and, if applicable, how a potentially Chinese claimant name influenced the analysis (increased caution or strong evidence to the contrary)."
    )))

    

    try:
        # Call the asynchronous Vertex GenAI function.
        ai_answer = await vertex_genai_multi_async(prompt_list)
        print(ai_answer)  # Print the raw AI response for debugging

        # --- Extract and process the main JSON result ---
        json_result = get_json(ai_answer)
        if isinstance(json_result, list): # !! we are expecting a dictionary tough 
            json_result.sort(key=lambda x: float(x.get("match", 0.0)), reverse=True)
            top_results = json_result[:3]
        elif isinstance(json_result, dict):
            # Sort by score
            sorted_items = sorted(json_result.items(),
                                  key=lambda item: float(item[1].get("match", 0.0)) if isinstance(item[1], dict) else 0.0,
                                  reverse=True)
            top_results = dict(sorted_items[:3])
        else:
            print("[DEBUG] Parsed JSON is not a list or a dictionary.")
            top_results = {}  # Initialize as empty dict

        # --- Extract Best Match Category (Separate from JSON) ---
        best_match_category = "Unknown"  # Default value
        lines = ai_answer.split('\n')
        for line in lines:
            if line.startswith("Best Match Category:"):
                best_match_category = line.replace("Best Match Category:", "").strip()
                break  # Stop after finding the first match

        # Extract the Explanation Summary
        explanation_summary = "Not found"  # Default value
        for line in ai_answer.split('\n'):
            if line.startswith("Explanation Summary:"):
                explanation_summary = line.replace("Explanation Summary:", "").strip()
                break  # Stop after finding the first match

        print(f"Explanation Summary: {explanation_summary}") # added print
        return top_results, best_match_category, explanation_summary # Return all three

    except Exception as e:
        print(f"Error in select_best_copyright_image_from_search_results: {str(e)}")
        return {}, "Unknown", "Not found"  # Return empty dict and "Unknown" on error

@observe()
async def get_copyright_images_from_google_api(df: pd.DataFrame, all_images_folder_path, best_images_folder_path, num_results=10):
    os.makedirs(best_images_folder_path, exist_ok=True)
    os.makedirs(all_images_folder_path, exist_ok=True)
    
    """
    Main function to process copyright records from a DataFrame,
    search for images, save them, and analyze them with Gemini.
    """
    results_list = []  # List to store results for each record
    for index, record in df.iterrows():
        claimant = clean_claimant(record.get("Copyright Claimant", ""))
        title = record.get("Title", "").strip()
        basis_of_claim = record.get("Basis of Claim", None)
        nation_of_publication = record.get("Nation of First Publication", None)
        search_query = f"{claimant} {title}"
        print(f"\n=== Processing query: {search_query} ===")
    # First, search and save images
        image_filenames_json, metadata_list_json = search_and_save_images(search_query, all_images_folder_path, num_results)
        try: 
            image_filenames = json.loads(image_filenames_json)
            metadata_list = json.loads(metadata_list_json)
        except:
            image_filenames = []

        if len(image_filenames) == 0:
            print(f"\033[91m❌❌❌ No images found by google for query: {search_query}\033[0m")
            continue
    
    # Create a list of tuples, each containing (image_path, metadata_dict)
        image_data_list = [(os.path.join(all_images_folder_path, filename), metadata) for filename, metadata in zip(image_filenames, metadata_list)]
        
        result_key_to_path = {}
        for i, (image_path, _) in enumerate(image_data_list):
            result_key = f"Result {i + 1}"
            result_key_to_path[result_key] = image_path
    # Then analyze with Gemini
        gemini_results, best_category, explanation_summary = await select_best_copyright_image_from_search_results(search_query, image_data_list,claimant, title, basis_of_claim, nation_of_publication)
    
        log_message("get_copyright_images_from_google_api: Gemini evaluation results:", gemini_results)

        best_match_score, best_match_path, best_result_key = -1.0, None, None  # Initialize best_result_key
        if isinstance(gemini_results, dict):
            for result_key, score_data in gemini_results.items():
                try:
                    score = float(score_data.get('match', -1.0))
                    if score > best_match_score:
                        best_match_score = score
                        best_result_key = result_key
                        # The best_match_path is now just the path, not a tuple
                        best_match_path = result_key_to_path.get(best_result_key)
                except (ValueError, IndexError, AttributeError) as e:
                    print(f"[ERROR] Error processing result {result_key}: {e}")


        results_dict = {}
    
        if best_match_path:
            original_filename = os.path.basename(best_match_path)
            base_filename, file_extension = os.path.splitext(original_filename)
            # Use sanitize_name to ensure the filename doesn't contain problematic characters
            safe_base_filename = sanitize_name(search_query.replace(" ", "_"))
            safe_result_key = sanitize_name(best_result_key.replace(" ", ""))

            best_filename_with_suffix = f"{safe_base_filename}_{safe_result_key}{file_extension}"
            best_image_path = os.path.join(best_images_folder_path, best_filename_with_suffix)

            # Add condition to only copy if probability is 0.85 or higher
            if best_match_score >= 0.85:
                # Copy the best image to the output folder
                try:
                    shutil.copy(best_match_path, best_image_path)
                    print(f"Copied best image to: {best_image_path}")
                except Exception as e:
                    print(f"Error copying image: {e}")

                # Store in results dictionary
                results_dict = {
                    "Best Image Path": best_image_path,
                    "Best Filename": best_filename_with_suffix,
                    "Gemini Results": gemini_results,
                    "Best Match Score": best_match_score,
                    "Best Match Category": best_category,
                    "Explanation Summary": explanation_summary,
                }

                df.at[index, 'best_google_image'] = best_image_path
                log_message(f"get_copyright_images_from_google_api: Best image selected: {best_filename_with_suffix} with score: {best_match_score}")

                langfuse_context.update_current_observation(output=f"Best image selected: {best_filename_with_suffix} with score: {best_match_score}")
                
            else:
                log_message(f"get_copyright_images_from_google_api: Best match score is below 0.85: ({best_match_score}), not keeping image.")
                langfuse_context.update_current_observation(output=f"Best match score is below 0.85: ({best_match_score}), not keeping image.")
        else:
            log_message("get_copyright_images_from_google_api: [WARNING] No best match found.")
            langfuse_context.update_current_observation(output="No best match found.")
        results_list.append(results_dict)
    # return results_list 
    return df


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()

    # Example DataFrame
    data = {'Copyright Claimant': ["April Major Rimpo"],
            'Title': ["1955 Essence"],
            'Basis of Claim': ["Registered Artwork"],
            'Nation of First Publication': ["United States"]}
    df = pd.DataFrame(data)

    folder_path = "D:\\Documents\\Programing\\TRO\\copyrights image from USCO"
    os.makedirs(folder_path, exist_ok=True)
    all_images_folder_path = os.path.join(folder_path, "all_images")
    os.makedirs(all_images_folder_path, exist_ok=True)
    best_images_folder_path = os.path.join(folder_path, "best_images")  
    os.makedirs(best_images_folder_path, exist_ok=True)
    
    num_results = 10

    # Use asyncio to run the async function
    results = asyncio.run(get_copyright_images_from_google_api(
        df=df,
        all_images_folder_path=all_images_folder_path,
        best_images_folder_path=best_images_folder_path,
        num_results=num_results
    ))
    
    print("Analysis results:", results) 