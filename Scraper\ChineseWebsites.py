#nofloqa
import asyncio, re, json, aiohttp, os, sys, mimetypes, hashlib, shutil
from PIL import Image # Pillow imports (ExifTags not needed)
import piexif # Added piexif import for EXIF manipulation
import glob
import json
sys.path.append(os.getcwd())
from bs4 import BeautifulSoup
from markdownify import markdownify
from AI.GC_VertexAI import vertex_genai_multi_async, vertex_genai_image_gen_async
from AI.GCV_GetImageParts import get_image_parts_async
from AI.LLM_shared import get_json, get_json_list
from Check.Do_Check_Download import download_from_url # Keep existing correct import
import Common.Constants as Constants
from langfuse.decorators import observe, langfuse_context
from Common.LangfuseClient import langfuse_client
from typing import Union, Tuple, List, Dict
from Scraper.reverseimage import reverse_search_and_download
from Scraper.deep_image_deduplicator import deduplicate_images, remove_text_duplicates

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# --- Website Configuration ---
WEBSITE_CONFIG = [
    {
        "name": "10100",
        "search_url_template": "https://www.10100.com/search/xxxx",
        "exclude_words": [], # Add specific words if needed for this site during URL finding
        "watermark_description": "blue and white diagonal retangle watermark", # To be used later if needed
        "image_download_pattern": r"/p/", # Pattern for images to download from this site
        "content_selector": None # None that can be identifies
        
    },
    {
        "name": "SellerDefense",
        "search_url_template": "https://sellerdefense.cn/?s=xxxx",
        "exclude_words": ["allcase"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "yellow diagonal SellerDefense watermark",
        "image_download_pattern": None, # No specific pattern for this site
        "content_selector": ".content"
    },
    {
        "name": "Maijiazhichi",
        "search_url_template": "https://maijiazhichi.com/?s=xxxx",
        "exclude_words": ["tro"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "grey-blue diagonal watermark",
        "image_download_pattern": None, # No specific pattern for this site
        "content_selector": ".entry-main"
    },
    # Add more site configurations as needed
]
# --------------------------

async def fetch_url(session: aiohttp.ClientSession, url: str) -> str | None:
    """Fetches content from a given URL."""
    try:
        async with session.get(url, timeout=40) as response:
            response.raise_for_status() # Raise an exception for bad status codes
            return await response.text()
    except aiohttp.ClientError as e:
        print(f"Error fetching {url}: {e}")
        return None
    except asyncio.TimeoutError:
        print(f"Timeout fetching {url}")
        return None

def format_case_number_for_site(case_number: str, site_name: str) -> str:
    """
    Generates the appropriate format for the case number based on the specific site.
    
    Args:
        case_number: e.g., "1:25-cv-00097"
        site_name: The name of the website to format for
        
    Returns:
        Formatted case number string specific to the site
    """
    match = re.match(r'\d+:(\d+)-([a-zA-Z]+)-(\d+)', case_number)
    if not match:
        print(f"Warning: Could not parse case number format: {case_number}")
        return ""

    part1 = match.group(1)
    part2 = match.group(2)
    part3 = match.group(3)
    part3_short = str(int(part3))  # Remove leading zeros

    # Format specifically for each site based on working examples
    if site_name == "10100":
        return f"{part1}-{part2}-{part3}"  # e.g., 25-cv-03439
    elif site_name == "SellerDefense":
        return f"{part1}-{part2}-{part3_short}"  # e.g., 25-cv-3439
    elif site_name == "Maijiazhichi":
        return f"{part1}-{part2}-{part3}"  # e.g., 25-cv-03439
    else:
        # Default format if site not recognized
        return f"{part1}-{part2}-{part3}"

@observe(capture_input=False)
async def A_find_target_urls_using_markdown(docket: str, plaintiff_name: str, session: aiohttp.ClientSession, 
                                           site_config: dict) -> list[str]:
    """Searches case format on a website using site-specific format, converts to Markdown, and extracts target article URLs via regex."""
    target_urls = set()  # Use a set to avoid duplicates

    # Get the site name and search URL template
    site_name = site_config["name"]
    search_url_template = site_config["search_url_template"]
    exclude_words = site_config["exclude_words"]
    
    # Format case number specifically for this site
    case_format = format_case_number_for_site(docket, site_name)
    if not case_format:
        return []

    # Determine base URL for resolving relative links found in markdown
    base_domain_match = re.match(r'(https?://[^/]+)', search_url_template)
    base_url = base_domain_match.group(1) if base_domain_match else None

    # Replace xxxx in template with the site-specific case format
    search_url = search_url_template.replace("xxxx", case_format)
    print(f"Searching (Markdown method): {search_url}")
    
    content = await fetch_url(session, search_url)
    if not content:
        return []

    # Convert HTML to Markdown
    try:
        # Use body_width=0 to prevent line wrapping that might break URLs
        markdown_content = markdownify(content, heading_style="ATX", bullets='*', body_width=0)
    except Exception as e:
        print(f"Error converting HTML to Markdown for {search_url}: {e}")
        return []

    prompt = f'{markdown_content} \n\n' + f'This page shows search results for {docket}. I am looking for information about a legal case (case number {docket} from plaintiff "{plaintiff_name}"). What are the relevant result for getting information about the case? For each result (hopefully just one), I need its title and its URL. Return your answer as a JSON array of objects, where each object has a "title" key and a "url" key. For example: [{{"title": "Result Title", "url": "/url/to/result"}}]. If there are no relevant results, you return and empty list []'
    response = await vertex_genai_multi_async([("text", prompt)], model_name="gemini-2.5-flash-preview-04-17", useVertexAI=False)
    json_list_response = get_json_list(response)
    
    for json_response in json_list_response:
        # Check if the URL contains any of the exclude words
        url = json_response.get("url")
        if any(word in url for word in exclude_words):
            continue
        else:
            url = base_url + url if url.startswith('/') else url
            target_urls.add(url)

    print(target_urls)
    langfuse_context.update_current_observation(
        input={"Docket": docket, "PlaintiffName": plaintiff_name, "SiteName": site_name, "SearchURL": search_url},
    )
    return list(target_urls)



# Step B: extract the Reg Numbers and Copyright images from the page
@observe(capture_input=False, capture_output=False)
async def B_extract_data_from_url(session: aiohttp.ClientSession, url: str, site_name: str, docket: str, plaintiff_name: str, date_filed: object) -> tuple[dict | None, dict]:
    """
    Fetches a target URL, converts to Markdown, processes images (downloading based on config),
    sends to LLM, and returns extracted data along with source info and a map of downloaded images.

    Args:
        session: The aiohttp client session.
        url: The URL of the page to process.
        site_name: The name of the website (used to get config).
        docket: The case docket number.
        date_filed: The date the case was filed (used for constructing download paths).

    Returns:
        A tuple: (json_response, downloaded_images_map)
        - json_response: The dictionary parsed from the LLM response, or None on error.
        - downloaded_images_map: A dictionary mapping {temp_persistent_path: original_image_url} for images downloaded.
    """
    downloaded_images_map = {} # {temp_persistent_path: original_image_url}
    print(f"Fetching data from: {url} (Site: {site_name})")
    content = await fetch_url(session, url)
    if not content:
        return None, downloaded_images_map

    soup = BeautifulSoup(content, 'html.parser')

    # Retrieve config for the current site
    site_config = next((config for config in WEBSITE_CONFIG if config["name"] == site_name), None)
    content_selector = None
    image_download_pattern = None
    if site_config:
        content_selector = site_config.get("content_selector")
        image_download_pattern = site_config.get("image_download_pattern")

    # Determine the HTML content to convert to Markdown
    html_to_convert = ""
    if content_selector:
        selected_element = soup.select_one(content_selector)
        if selected_element:
            html_to_convert = str(selected_element)
        else:
            print(f"Warning: Content selector '{content_selector}' not found for {url}. Falling back to body.")
            html_to_convert = str(soup.body) if soup.body else str(soup)
    else:
        html_to_convert = str(soup.body) if soup.body else str(soup) # Fallback to whole body if no selector

    # Convert the relevant HTML part to Markdown
    markdown_content = markdownify(html_to_convert, heading_style="ATX", bullets='*', body_width=0)

    # --- Image Processing and Input List Construction ---
    prompt_input_list = []
    # Define persistent download directory
    case_folder = Constants.sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket}")
    copyright_cn_allpictures_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "copyright_cn_allpictures")
    os.makedirs(copyright_cn_allpictures_dir, exist_ok=True)

    last_end = 0
    image_index = 0
    # Regex to find Markdown images: ![alt text](url)
    for match in re.finditer(r'!\[.*?\]\((.*?)\)', markdown_content):
        # Append text chunk before the image
        text_chunk = markdown_content[last_end:match.start()]
        if text_chunk: prompt_input_list.append(("text", text_chunk))

        full_image_url = match.group(1)
        
        # If (pattern not set) or (pattern is set & found)
        if (not image_download_pattern or re.search(image_download_pattern, full_image_url)) and "data:image/" not in full_image_url:
            original_filename = full_image_url.split("?")[0].split("/")[-1] # Get the last part of the URL before any query string
            temp_persistent_path = os.path.join(copyright_cn_allpictures_dir, original_filename)
            await download_from_url(full_image_url, temp_persistent_path)

            # If download succeeded (or file already existed), add to prompt and map
            if os.path.exists(temp_persistent_path):
                image_index += 1
                prompt_input_list.append(("text", f"\n[Provided Image Identifier: Image_{image_index}]\n"))
                prompt_input_list.append(("image_path", temp_persistent_path))
                downloaded_images_map[f"Image_{image_index}"] = (temp_persistent_path, full_image_url) # Still useful for metadata

        last_end = match.end()

    # Append the final text chunk
    final_text_chunk = markdown_content[last_end:]
    if final_text_chunk: prompt_input_list.append(("text", final_text_chunk))

    # Filter out empty text items
    prompt_input_list = [item for item in prompt_input_list if item[0] != "text" or item[1].strip()]

    # --- End Image Processing ---

    # Prepare the LLM prompt (Instruction part)
    prompt = f"""
Analyze the following content, which consists of text and images, extracted from the webpage {url}.
Identify and extract all intellectual property registration numbers mentioned related to legal case {docket} filed by plaintiff {plaintiff_name}.
Specifically look for:
1.  Trademark Registration Numbers (usually start with digits or contain specific keywords like "Trademark Reg. No.")
2.  Patent Registration Numbers (often start with "US" followed by digits, or contain keywords like "Patent No.")
3.  Copyright Registration Numbers (often start with "VA", "TX", "SR", or contain keywords like "Copyright Reg. No.")

For copyright you also provide the image identifier of the image that appear to be related to Copyright registrations. Try to associate each copyright image with a specific Copyright Registration Number if possible. If an image is present but no clear registration number is nearby, list the image URL with a placeholder key like "no_reg_X". If a registration number is found with no associated image, list it with a null value for the image URL.
Additionally, check the page (especially near copyright information) for a single URL pointing to the artist's main website or portfolio (artist_url). If found, include it as a top-level key in the JSON.

Base your analysis *only* on the text provided and the content of the images. Do not infer or invent information (such as registration numbers) that is not explicitly present in the text or visible in the images.

Return the extracted information ONLY as a JSON object with the following structure:
{{
  "trademarks": ["list of strings"],
  "patents": ["list of strings"],
  "copyrights": [
    {{ "reg_no": "VAx123456", "identifier": "image_identifier_a"}},
    {{ "reg_no": "VAx234567", "identifier": null}},
    {{ "reg_no": "no_reg_1", "identifier": "image_identifier_b"}},
    {{ "reg_no": "multi", "identifier": "image_identifier_c"}}
  ],
  "artist_url": "url_string_or_null"
}}
where
- "copyrights": A list of JSON objects. Each object should have a "reg_no" key (string, for the registration number, or a placeholder like "no_reg_1", or "multi" if the image has multiple copyrighted images in a single image) and an "identifier" key (string, for the image identifier found in the markdown, or null if no image is associated).
- "artist_url": Contains the single artist website URL found on the page, or null if none was found.

Do not include any introductory text, explanations, or markdown formatting in your response. Just the JSON object.
"""
    # Construct the final input for the LLM
    llm_input = [("text", prompt)] + prompt_input_list

    # Make the LLM call
    response = await vertex_genai_multi_async(llm_input, model_name="gemini-2.5-flash-preview-04-17", useVertexAI=False) # gemini-2.5-pro-exp-03-25

    # Process LLM response
    try:
        json_response = get_json(response)
        # Add source information to the result
        if isinstance(json_response, dict):
            json_response["source_page_url"] = url
            json_response["source_site"] = site_name
    except json.JSONDecodeError:
        print(f"Error decoding LLM response for {url}: {response}")
        json_response =  None
    except Exception as e:
        print(f"Unexpected error processing LLM response for {url}: {e}")
        json_response = None

    langfuse_context.update_current_observation(
        input={"URL": url, "SiteName": site_name},
        output={"LLMResponse": json_response if json_response is not None else 'None/Error'}
    )
    
    return json_response, downloaded_images_map # Return map along with response,  Still return map even if LLM fails
    

# Step C: process the copyrighted images

@observe()
async def C_copyright_process_picture(reg_no, img_url, case_folder, source_page_url, source_site, watermark_description, download_path: str | None = None):
    """Downloads original, adds metadata to it, removes watermark using GenAI, saves clean version."""
    
    # Define directories
    watermarked_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "copyright_cn_selected_watermarked")
    final_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder) # Directory for watermark-removed files
    
    # Create temporary directories for reverse search results
    reverse_search_output_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "reverse_search_results")
    reverse_search_final_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "reverse_search_final")
    
    # Create directory for duplicate images
    duplicate_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "duplicate_images")
    
    os.makedirs(watermarked_dir, exist_ok=True) # Ensure watermarked directory exists
    os.makedirs(final_dir, exist_ok=True) # Ensure final directory exists
    os.makedirs(reverse_search_output_dir, exist_ok=True) # Ensure reverse search output directory exists
    os.makedirs(reverse_search_final_dir, exist_ok=True) # Ensure reverse search final directory exists
    os.makedirs(duplicate_dir, exist_ok=True) # Ensure duplicate directory exists
    
    # First deduplicate existing images in the watermark folder - do this BEFORE adding new images
    # Note: We're keeping text images but removing duplicates
    deduplicate_images(watermarked_dir, duplicate_dir)
    
    # Define paths
    try:
        # Guess extension or use a default if split fails
        url_parts = img_url.split(".")
        file_extension = url_parts[-1] if len(url_parts) > 1 else "jpg" # Default extension
    except Exception:
        file_extension = "jpg" # Fallback extension

    try:
        # Determine the final path for the original image (used for metadata and GenAI input)
        watermark_path = os.path.join(watermarked_dir, f"{Constants.sanitize_name(reg_no)}_original.{file_extension}")
        # Copy the file to the final original name (direct operation)
        shutil.copy(download_path, watermark_path)
        print(f"Copied download {download_path} -> {watermark_path}")
        
        # 1. Check image format and convert if necessary before adding metadata
        try:
            with Image.open(watermark_path) as img:
                img_format = img.format.upper() # Get format (JPEG, PNG, etc.)
                if img_format not in ['JPEG', 'TIFF']:
                    print(f"Original format {img_format} not supported by piexif. Converting {reg_no} to JPEG.")
                    # Ensure image is in RGB mode for JPEG saving
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Create new path for JPEG version
                    base, _ = os.path.splitext(watermark_path)
                    new_jpeg_path = f"{base}.jpg"
                    
                    # Save as JPEG
                    img.save(new_jpeg_path, "JPEG")
                    print(f"Saved converted JPEG to: {new_jpeg_path}")
                    
                    # Remove original non-JPEG/TIFF file
                    try:
                        os.remove(watermark_path)
                        print(f"Removed original file: {watermark_path}")
                    except OSError as rm_err:
                        print(f"Warning: Could not remove original file {watermark_path} after conversion: {rm_err}")
                        
                    # Update path to point to the new JPEG file
                    watermark_path = new_jpeg_path
                    file_extension = "jpg"
                    
        except FileNotFoundError:
             print(f"Error: Downloaded file not found at {watermark_path} before format check.")
             raise # Re-raise the error to be caught by the outer try-except
        except Exception as img_err:
            print(f"Error during image format check/conversion for {reg_no}: {img_err}")
            raise # Re-raise to be caught by outer try-except

        # 2. Add Metadata to Original File (now guaranteed to be JPEG/TIFF or skipped)
        metadata = {
            "source_site": source_site,
            "source_page_url": source_page_url,
            "original_image_url": img_url,
            "processing_step": "original_download" # Indicate this is the original
        }
        metadata_json = json.dumps(metadata)
        try:
            # Load original image's EXIF, add comment, insert back into file
            exif_dict = piexif.load(watermark_path)
            comment_bytes = metadata_json.encode('utf-8')
            exif_dict["Exif"][piexif.ExifIFD.UserComment] = comment_bytes
            exif_bytes = piexif.dump(exif_dict)
            piexif.insert(exif_bytes, watermark_path) # Insert EXIF into the original file
            print(f"Added metadata to original file: {watermark_path}")
        except Exception as meta_err:
            # If EXIF loading/insertion fails (e.g., non-JPEG), we skip adding metadata but continue
            print(f"Warning: Could not add metadata to original file {watermark_path}: {meta_err}")
        
        # Check for duplicates after adding the current image to the watermarked directory
        # This ensures we don't process images we already have
        deduplicate_images(watermarked_dir, duplicate_dir)
        
        # Check if our image was moved to duplicates, if so we should skip processing
        if not os.path.exists(watermark_path):
            print(f"Image {reg_no} was identified as a duplicate and moved to {duplicate_dir}")
            # Try to find the original in the final directory that matches this registration number
            existing_finals = glob.glob(os.path.join(final_dir, f"{Constants.sanitize_name(reg_no)}*"))
            if existing_finals:
                print(f"Found existing processed images for {reg_no} in final directory")
                return [{"reg": reg_no, "image_path": path} for path in existing_finals]
            else:
                # We need to find the duplicate that matches this image to use it
                duplicate_files = glob.glob(os.path.join(duplicate_dir, "*"))
                # For simplicity, just return an empty list - the caller should handle this case
                print(f"No existing processed images found for duplicate {reg_no}")
                return []
            
        # Initialize list to track final processed images
        final_image_paths = []
        
        # Process multi-images or single image
        if reg_no.startswith("multi"):
            print(f"Using get_image_parts_async for {reg_no}")
            images_parts_with_label = await get_image_parts_async("Individual images that might be copyrighted", watermark_path)
            # images_parts_with_label is a list of json: [{"label": parts_details[i]["label"], "path": part_path, "width": part.shape[1], "height": part.shape[0]}]
            images_to_search = [image_json["path"] for image_json in images_parts_with_label]
            
            # Create a temporary directory for split files
            temp_splits_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "temp_splits")
            os.makedirs(temp_splits_dir, exist_ok=True)
            
            # Copy all split images to temp directory
            unique_images_to_search = []
            for idx, img_path in enumerate(images_to_search):
                temp_split_path = os.path.join(temp_splits_dir, f"split_{idx}_{os.path.basename(img_path)}")
                shutil.copy(img_path, temp_split_path)
            
            # Run deduplication on temp dir
            deduplicate_images(temp_splits_dir, duplicate_dir)
            
            # Only keep files that weren't moved to duplicates
            for idx, img_path in enumerate(images_to_search):
                temp_split_path = os.path.join(temp_splits_dir, f"split_{idx}_{os.path.basename(img_path)}")
                if os.path.exists(temp_split_path):
                    unique_images_to_search.append(img_path)
                    os.remove(temp_split_path)  # Clean up temp file
                else:
                    print(f"Split image {img_path} was identified as a duplicate")
            
            # Use only non-duplicate images for further processing
            images_to_search = unique_images_to_search
            
            # For multi-images, simply save each split to final_dir
            for idx, image_path in enumerate(images_to_search):
                base_name = f"{Constants.sanitize_name(reg_no)}_split_{idx+1}"
                split_extension = os.path.splitext(image_path)[1]
                split_final_path = os.path.join(final_dir, f"{base_name}_original{split_extension}")
                
                # Check if this split already exists in final_dir to avoid duplicates
                if not os.path.exists(split_final_path):
                    shutil.copy2(image_path, split_final_path)
                    print(f"Multi-image: Saved split {idx+1} to final directory: {split_final_path}")
                else:
                    print(f"Multi-image: Split {idx+1} already exists in final directory: {split_final_path}")
                
                final_image_paths.append({"reg": reg_no, "image_path": split_final_path})
                
        else:
            # For single images, process with reverse search or GenAI
            base_name = Constants.sanitize_name(reg_no)
            
            # Check if we already have a processed version of this image in final directory
            existing_finals = glob.glob(os.path.join(final_dir, f"{base_name}*"))
            if existing_finals:
                print(f"Found existing processed images for {reg_no} in final directory")
                # Return existing processed images
                return [{"reg": reg_no, "image_path": path} for path in existing_finals]
            
            # Perform reverse image search
            print(f"Performing reverse image search for {watermark_path}")
            reverse_search_results = reverse_search_and_download(
                watermark_path, 
                output_folder=reverse_search_output_dir, 
                final_folder=reverse_search_final_dir, 
                max_images=10
            )
            
            # Check for existing high-similarity images
            print(f"Checking for existing high-similarity images in: {reverse_search_final_dir}")
            final_json_path = os.path.join(reverse_search_final_dir, "final_image_urls.json")
            
            high_similarity_image = None
            highest_similarity = 0.0
            
            # Check for existing JSON data with similarity scores
            if os.path.exists(final_json_path):
                try:
                    with open(final_json_path, 'r') as f:
                        final_image_data = json.load(f)
                    
                    # Find the image with highest similarity above threshold
                    for img_name, data in final_image_data.items():
                        if isinstance(data, dict) and 'similarity' in data:
                            similarity = float(data['similarity'])
                            if similarity > highest_similarity:
                                highest_similarity = similarity
                                high_similarity_image = os.path.join(reverse_search_final_dir, img_name)
                                print(f"Found high-similarity image in final folder: {img_name}, similarity={similarity}")
                except Exception as e:
                    print(f"Error reading final image data JSON: {e}")
            
            # If no image found in JSON, check all images in the directory
            if not high_similarity_image:
                # Check for the highest similarity in the reverse search results
                for result in reverse_search_results:
                    if "similarity" in result and result["similarity"] > highest_similarity:
                        highest_similarity = result["similarity"]
                        if "path" in result:
                            high_similarity_image = result["path"]
            
            # Log the best match information
            print(f"Best reverse search match for {base_name}: similarity={highest_similarity}, path={high_similarity_image}")
            
            # Process based on similarity threshold
            if highest_similarity > 0.4 and high_similarity_image and os.path.exists(high_similarity_image):
                # High-similarity image found, use this one
                print(f"Found high-similarity match ({highest_similarity}) for {watermark_path}: {high_similarity_image}")
                # Copy to final directory - ensure we use a consistent naming pattern
                dest_filename = f"{base_name}_reverse_search{os.path.splitext(high_similarity_image)[1]}"
                dest_path = os.path.join(final_dir, dest_filename)
                
                # Only copy if it doesn't already exist in final_dir
                if not os.path.exists(dest_path):
                    shutil.copy2(high_similarity_image, dest_path)
                    print(f"Using high-similarity image from reverse search: {dest_path}")
                else:
                    print(f"High-similarity image already exists in final directory: {dest_path}")
                
                final_image_paths.append({"reg": reg_no, "image_path": dest_path})
            else:
                # For regular images with low similarity, use GenAI for watermark removal
                print(f"No high-similarity images found. Using GenAI for watermark removal.")
                watermark_removal_prompt = f"Your job is to generate a picture exactly the same but remove the {watermark_description}"
                print(f"Using watermark removal prompt for {reg_no}: '{watermark_removal_prompt}'")
                
                # Check if we already have a GenAI processed version in the final directory
                genai_pattern = os.path.join(final_dir, f"{base_name}_genai*")
                existing_genai = glob.glob(genai_pattern)
                
                if existing_genai:
                    print(f"Found existing GenAI processed image for {reg_no}: {existing_genai[0]}")
                    final_image_paths.append({"reg": reg_no, "image_path": existing_genai[0]})
                else:
                    # Generate new image with GenAI
                    inline_data = await vertex_genai_image_gen_async(
                        [("text", watermark_removal_prompt), ("image_path", watermark_path)]
                    )

                    if inline_data and not isinstance(inline_data, str):  # Check if we got image data
                        processed_image_bytes = inline_data.data
                        mime_type = inline_data.mime_type
                        genai_extension = mimetypes.guess_extension(mime_type) or f".{mime_type.split('/')[-1]}"
                        
                        # Define final path for the watermark-removed image
                        final_image_path_base = os.path.join(final_dir, f"{base_name}_genai")
                        final_image_path = f"{final_image_path_base}{genai_extension}"

                        # Save GenAI Result
                        try:
                            with open(final_image_path, "wb") as f:
                                f.write(processed_image_bytes)
                            print(f"Saved watermark-removed image for {reg_no} to {final_image_path}")
                            final_image_paths.append({"reg": reg_no, "image_path": final_image_path})
                        except Exception as save_err:
                            print(f"Error saving watermark-removed image for {reg_no}: {save_err}")
                            # Fallback: if GenAI fails, use original image
                            orig_final_path = os.path.join(final_dir, f"{base_name}_original.{file_extension}")
                            # Check if original already exists in final_dir
                            if not os.path.exists(orig_final_path):
                                shutil.copy2(watermark_path, orig_final_path)
                                print(f"Fallback to original image: {orig_final_path}")
                            else:
                                print(f"Original image already exists in final directory: {orig_final_path}")
                            final_image_paths.append({"reg": reg_no, "image_path": orig_final_path})
                    else:
                        print(f"GenAI did not return image data for {reg_no}. Using original image as fallback.")
                        # Fallback: if GenAI fails, use original image
                        orig_final_path = os.path.join(final_dir, f"{base_name}_original.{file_extension}")
                        # Check if original already exists in final_dir
                        if not os.path.exists(orig_final_path):
                            shutil.copy2(watermark_path, orig_final_path)
                            print(f"Fallback to original image: {orig_final_path}")
                        else:
                            print(f"Original image already exists in final directory: {orig_final_path}")
                        final_image_paths.append({"reg": reg_no, "image_path": orig_final_path})
        
        # Important: Check if we need to add original images when no processing was done
        if not final_image_paths:
            print(f"No processed images found for {reg_no}, using originals as fallback")
            if reg_no.startswith("multi"):
                # For multi-images, try to find any split images to use
                existing_multi = glob.glob(os.path.join(final_dir, f"{Constants.sanitize_name(reg_no)}_split_*"))
                if existing_multi:
                    print(f"Found existing split images for {reg_no} in final directory")
                    final_image_paths = [{"reg": reg_no, "image_path": path} for path in existing_multi]
                else:
                    # If no existing splits, copy from temp splits if available
                    for idx, image_path in enumerate(images_to_search):
                        base_name = f"{Constants.sanitize_name(reg_no)}_split_{idx+1}"
                        dest_extension = os.path.splitext(image_path)[1]
                        dest_path = os.path.join(final_dir, f"{base_name}_original{dest_extension}")
                        if not os.path.exists(dest_path):
                            shutil.copy2(image_path, dest_path)
                        final_image_paths.append({"reg": reg_no, "image_path": dest_path})
            else:
                # For single images, check if original exists in final_dir
                orig_path = os.path.join(final_dir, f"{Constants.sanitize_name(reg_no)}_original.{file_extension}")
                if not os.path.exists(orig_path):
                    shutil.copy2(watermark_path, orig_path)
                else:
                    print(f"Original image already exists in final directory: {orig_path}")
                final_image_paths.append({"reg": reg_no, "image_path": orig_path})
        
        # Remove duplicates from final_image_paths by path
        unique_paths = set()
        unique_final_image_paths = []
        for item in final_image_paths:
            if item["image_path"] not in unique_paths:
                unique_paths.add(item["image_path"])
                unique_final_image_paths.append(item)
        
        print(f"Final images for {reg_no}: {[path['image_path'] for path in unique_final_image_paths]}")
        return unique_final_image_paths

    except Exception as e:
        print(f"Error during download/metadata/genai process for {reg_no}: {e}")
        # Clean up potentially incomplete original download if it exists and an error occurred
        if 'watermark_path' in locals() and os.path.exists(watermark_path) and 'final_image_path' not in locals(): # Only delete if processing failed
             try:
                 os.remove(watermark_path)
                 print(f"Removed incomplete original file: {watermark_path}")
             except OSError as rm_err:
                 print(f"Error removing incomplete original file {watermark_path}: {rm_err}")

        # In case of error, try to return original image if available
        if 'watermark_path' in locals() and os.path.exists(watermark_path):
            dest_filename = f"{Constants.sanitize_name(reg_no)}_error_original{os.path.splitext(watermark_path)[1]}"
            dest_path = os.path.join(final_dir, dest_filename)
            try:
                # Only copy if it doesn't already exist
                if not os.path.exists(dest_path):
                    shutil.copy2(watermark_path, dest_path)
                    print(f"Error recovery: saved original image to {dest_path}")
                else:
                    print(f"Error recovery: original image already exists at {dest_path}")
                return [{"reg": reg_no, "image_path": dest_path}]
            except Exception as copy_err:
                print(f"Error during recovery copy: {copy_err}")
        
        return []

@observe()
async def scrape_case_data(date_filed: str, docket: str, plaintiff_name: str, required_ip: List[str]) -> Dict:
    """Scrape IP-related data for a given case docket across multiple sources."""

    case_folder = Constants.sanitize_name(f"{date_filed.strftime('%Y-%m-%d')}-{docket}")
    downloads_folder = os.path.join("downloads", case_folder)
    duplicates_folder = os.path.join(downloads_folder, "duplicates")
    os.makedirs(downloads_folder, exist_ok=True)
    os.makedirs(duplicates_folder, exist_ok=True)

    all_trademarks, all_patents, all_artist_urls = set(), set(), set()
    all_copyrights_image_paths, results = [], []

    async with aiohttp.ClientSession() as session:
        # Discover URLs to crawl from each configured website - using site-specific format
        url_results = await asyncio.gather(*[
            A_find_target_urls_using_markdown(
                docket, plaintiff_name, session, config
            ) for config in WEBSITE_CONFIG
        ])

        # Extract data for unique URLs
        processed_urls = set()
        extraction_tasks = []

        for i, config in enumerate(WEBSITE_CONFIG):
            for url in url_results[i]:
                if url not in processed_urls:
                    extraction_tasks.append(
                        B_extract_data_from_url(session, url, config["name"], docket, plaintiff_name, date_filed)
                    )
                    processed_urls.add(url)

        print(f"Scheduled {len(extraction_tasks)} unique URLs for data extraction.")
        results = await asyncio.gather(*extraction_tasks)

    # Rest of the function remains unchanged
    # Deduplicate persistent images in copyright_cn_allpictures
    copyright_cn_allpictures_dir = os.path.join(
        os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "copyright_cn_allpictures"
    )
    duplicates_subfolder = os.path.join(copyright_cn_allpictures_dir, "duplicates")
    deduplicate_images(copyright_cn_allpictures_dir, duplicates_subfolder)
    deduped_filenames = {
        os.path.basename(f) for f in os.listdir(copyright_cn_allpictures_dir)
        if os.path.isfile(os.path.join(copyright_cn_allpictures_dir, f))
    }

    multi_index, no_reg_index = 1, 1
    final_results = []

    for data, img_map in results:
        if not isinstance(data, dict):
            continue

        all_trademarks.update(data.get("trademarks", []) if "trademark" in required_ip else [])
        all_patents.update(data.get("patents", []) if "patent" in required_ip else [])
        source_url = data.get("source_page_url", "Unknown")
        source_site = data.get("source_site", "Unknown")

        if artist_url := data.get("artist_url"):
            all_artist_urls.add(artist_url)

        if "copyright" not in required_ip:
            continue

        for item in data.get("copyrights", []):
            reg_no, identifier = item.get("reg_no"), item.get("identifier")
            if not reg_no or (identifier and not isinstance(identifier, str)):
                print(f"Warning: Skipping malformed item: {item}")
                continue

            img_path, img_url = img_map.get(identifier, (None, None)) if identifier else (None, None)

            if not img_path or os.path.basename(img_path) not in deduped_filenames or not os.path.exists(img_path):
                continue

            if img_path not in all_copyrights_image_paths:
                all_copyrights_image_paths.append(img_path)

                reg_no = (
                    f"multi_{multi_index}" if reg_no.startswith("multi") else
                    f"no_reg_{no_reg_index}" if reg_no.startswith("no_reg_") else
                    reg_no
                )
                if reg_no.startswith("multi"):
                    multi_index += 1
                elif reg_no.startswith("no_reg_"):
                    no_reg_index += 1

                try:
                    print(f"Processing image for reg_no '{reg_no}'")
                    processed = await C_copyright_process_picture(
                        reg_no, img_url, case_folder, source_url, source_site,
                        next((s["watermark_description"] for s in WEBSITE_CONFIG if s["name"] == source_site), "the watermark"),
                        img_path
                    )
                    if processed:
                        final_results.extend(processed)
                except Exception as e:
                    print(f"Error processing reg_no '{reg_no}': {e}")

    return {
        "trademarks": sorted(all_trademarks),
        "patents": sorted(all_patents),
        "copyrights": {
            r["reg"]: r["image_path"] for r in final_results if r.get("image_path")
        },
        "artist_urls": sorted(all_artist_urls)
    }

# Example Usage (can be run with `python -m Scraper.scraper`)
if __name__ == "__main__":
    async def main():
        import pandas as pd
        from DatabaseManagement.ImportExport import get_table_from_GZ
        date = pd.to_datetime("2025-03-14")
        # test_case_docket = "1:25-cv-02710"
        test_case_docket = "1:24-cv-24824"
        # test_case_docket = "1:25-cv-00097"
        test_case_docket = "1:23-cv-15810"

        plaintiff_name = None # Initialize plaintiff_name

        case_where_clause = f"docket = '{test_case_docket}'"
        # Using force_refresh=False to align with previous behavior for these lookups.
        df_case = get_table_from_GZ("tb_case", force_refresh=False, where_clause=case_where_clause)

        if not df_case.empty:
            plaintiff_id = df_case["plaintiff_id"].values[0]
            
            plaintiff_where_clause = f"id = {plaintiff_id}" # id is numeric as confirmed
            df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False, where_clause=plaintiff_where_clause)
            
            if not df_plaintiff.empty:
                plaintiff_name = df_plaintiff["plaintiff_name"].values[0]
            else:
                print(f"Plaintiff with ID {plaintiff_id} not found for case {test_case_docket}.")
                # plaintiff_name remains None
        else:
            print(f"Case with docket {test_case_docket} not found.")
            # plaintiff_name remains None

        if plaintiff_name: # Proceed only if plaintiff_name was found
            print(f"Scraping data for case: {test_case_docket} (Plaintiff: {plaintiff_name})")
            data = await scrape_case_data(date, test_case_docket, plaintiff_name, ["patent", "trademark", "copyright"])
            print("\n--- Final Result ---")
            print(json.dumps(data, indent=2))
        else:
            print(f"Could not retrieve plaintiff name for case {test_case_docket}. Skipping scrape.")

    asyncio.run(main())