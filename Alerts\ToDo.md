1. Are we missing the plaintiff overview and translation?
2. We delete the image folder, then use "resume mode" then upload the folder to NAS. Are we loosing the images from chinese website?
3. In a given step, if all the PDFs are local already, we open the download modal 3 times!
4. Platform to review copyright and approave pics by pics
5. For scrape_date_range: we need to save the cases that do not meet the conditions? Do that we dont need to open the page for nothing?

When save_to_db is false, we are still inserting data into database for : inserting new case (get ID) and new plaintiff?

resume mode:

- still sending pictures to COS and into
- steps_df:
  - files_downloaded and failed get reinit to 0
  - all translation is redone
  - case_id is missing

steps_df.loc[steps_df['row_index'] == target_step_row_index, 'files_failed'] += 1 - num_downloaded  # Expecting 1 main file  : This is for steps with exhibit. How about if they have multiple files? The result will be negative

Merge process_copyright_regno and process_copyright_registration_numbers, and same for trademark and patent

suggestion on the 1+Deep improvement. download the exhibit when there is "unredacted". even though there is a "sealed"
Sealed: sometimes: the sealed steps has some documents available
Sometime the Answer is "Motion by Defandant"
Sometimes the IP is in exhibits to the "Motion by Defandant"

In AI task: refresh_cached_data() : not sure what it does because force == False.

Trademark by name:

- When we have multiple Owners on USPTO we could ask the AI which one it is

**Fix this:**
case ID 13677, 1:25-cv-04962...
INFO: Relevant steps to be considered:
    step_nb               priority_name                                    proceeding_text
1     2.00          PRIORITY_COMPLAINT  COMPLAINT against 2foxieh, ABQP WHHR Bags Stor...
26   25.00  PRIORITY_AMENDED_COMPLAINT  AMENDED COMPLAINT against 2FOXIEH, ABQP WHHR B...
32   31.00                PRIORITY_TRO  MOTION to Dissolve Temporary Restraining Order...
2     3.00  PRIORITY_PRELIM_INJUNCTION  MOTION for Temporary Restraining Order, MOTION...


**Path:**
Main doc => relevant and reg_no
    Trademark reg_no whole_set => get from USPTO
    Patent reg_no whole_set => get from USPTO
    Copyright target main doc is True and target exhibit is emoty => process_copyright_regno
    Get exhibit (either mentionned or all)
        Trademark: Objective: get the whole_set of reg_no using LLM => get from USPTO
            if cannot find on USPTO -> extract form Exhibit (need to get rid of that!)
        Patent: Objective: get the whole_set of reg_no using LLM => get from USPTO
        Copyright: try to extract images
    Chinese Websites:

    By name:

Note:
process_copyright_regno =
    search for picture inside the provided PDF, e.g. as a table
    if not found:
        get the LLM to extract the artist and title
        if not found:
            get artist and title on USCO
        Search google
