import re
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
import time
from difflib import Sequence<PERSON>atcher
import traceback
from ..Chrome_Driver import move_mouse_to, send_keyboard_input, random_delay
from Alerts.Save_Browser import save_browser_state
from logdata import log_message
from Common.Constants import court_mapping, STATE_ABBREVIATIONS, STATE_NAME_FROM_COURT, DISTRICT_INITIALS


def build_docketbird_url(court, docket_number):
    """
    Build a direct URL to the DocketBird case page from court name and docket number.
    Uses standardized court names and state abbreviations for reliability.

    Args:
        court (str): The court name (e.g., "Texas Eastern", "US District Court for the District of Arizona")
        docket_number (str): The case docket number (e.g., "1:25-cv-00097")

    Returns:
        str: The full DocketBird URL for the case, or None if generation fails.
    """
    court_prefix = None
    standardized_court = court_mapping.get(court)

    if not standardized_court:
        # If the input court name isn't in the mapping, check if it's already standardized
        if court in STATE_NAME_FROM_COURT:
             standardized_court = court
        else:
            log_message(f"Court name '{court}' not found in standardization mapping.")
            # Attempt basic parsing as a fallback (less reliable)
            words = court.split(' ')
            if len(words) >= 2 and len(words[0]) >= 2 and len(words[1]) >= 1:
                 # Use original less reliable logic only as fallback
                 first_word_abbr = words[0][:2].lower()
                 second_word_initial = words[1][0].lower()
                 # Check if first word looks like a state abbreviation exception
                 abbr_map = {v: k for k, v in STATE_ABBREVIATIONS.items()} # Reverse map abbr->State
                 if first_word_abbr.upper() in abbr_map:
                     state_name = abbr_map[first_word_abbr.upper()]
                     if state_name in ['Arizona', 'Connecticut', 'Hawaii', 'Virginia', 'Vermont', 'Tennessee', 'West Virginia']:
                         # Use correct abbr if it matches an exception state
                         pass # first_word_abbr is already correct
                     elif state_name == 'District of Columbia':
                         first_word_abbr = 'dc'
                 court_prefix = f"{first_word_abbr}{second_word_initial}d"
                 log_message(f"Using fallback logic for court '{court}', generated prefix: {court_prefix}", level='WARNING')
            else:
                 log_message(f"Cannot determine standardized court or apply fallback for: {court}")


    if standardized_court:
        state_name = STATE_NAME_FROM_COURT.get(standardized_court)
        state_abbr = STATE_ABBREVIATIONS.get(state_name)

        if not state_abbr:
            log_message(f"Could not find state abbreviation for state: {state_name} derived from court: {standardized_court}")
        else:
            district_initial = ''
            # Check for district name in the standardized court name
            for district, initial in DISTRICT_INITIALS.items():
                if district in standardized_court:
                    district_initial = initial
                    break
            court_prefix = f"{state_abbr.lower()}{district_initial}d"

    # Process Docket to expand year (e.g., "1:25-cv-00097" -> "1:2025-cv-00097")
    processed_docket = None
    if docket_number and isinstance(docket_number, str):
        # Match pattern like '1:25-cv-00097' to insert '20'
        match = re.match(r'^([^:]*:)(\d{2})(-[a-zA-Z]{2,}-\d+)$', docket_number) # Allow diff case types like mj, cr
        if match:
            prefix = match.group(1)    # e.g., '1:'
            year = match.group(2)      # e.g., '25'
            suffix = match.group(3)    # e.g., '-cv-00097'
            # Prepend '20' to the two-digit year
            processed_docket = f"{prefix}20{year}{suffix}"  # e.g., '1:2025-cv-00097'
        else:
             # If the format doesn't match the YY pattern, assume it's already correct or a different format
             processed_docket = docket_number
             log_message(f"Docket format '{docket_number}' doesn't match YY pattern for year expansion, using as is.")


    # Construct the full URL
    if court_prefix and processed_docket:
        case_id = f"{court_prefix}-{processed_docket}"
        log_message(f"Generated DocketBird URL parts: case_id={case_id} for court='{court}', docket='{docket_number}'")
        return f"https://www.docketbird.com/cases?case_id={case_id}"
    else:
        log_message(f"Failed to generate DocketBird link components for court '{court}' and docket '{docket_number}' (Prefix: {court_prefix}, Processed Docket: {processed_docket})")
        return None


def navigate_to_docketbird_case(driver, court, docket_number, title=None):
    """
    Navigate directly to the DocketBird case page using the constructed URL.
    
    Args:
        driver: Selenium WebDriver instance
        court (str): The court name
        docket_number (str): The case docket number
        title (str, optional): The case title (not used for navigation but kept for compatibility)
    
    Returns:
        bool: True if navigation successful, False otherwise
    """
    try:
        url = build_docketbird_url(court, docket_number)
        if not url:
            return False
        
        # Navigate to the case directly
        driver.get(url)
        
        # Wait for the docket sheet to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "docket_sheet"))
        )
        
        log_message(f"Successfully navigated to case {docket_number} in {court}")
        return True
        
    except Exception as e:
        log_message(f"Error navigating to case for {docket_number} in {court}: {e}")
        error_msg = str(e)
        try:
            error_dir = save_browser_state(driver, error_msg, traceback.format_exc())
            log_message(f"Error state saved to: {error_dir}", level='ERROR')
        except Exception as save_error:
            log_message(f"Failed to save error state: {save_error}", level='ERROR')
        
        log_message(f"Traceback:\n{traceback.format_exc()}")
        return False


# Keeping the old function for reference, but it should not be used anymore
# def docketbird_search_a_case_by_docket(driver, docket_number, court, title):
#     driver.get('https://www.docketbird.com/find-case')  # return to search page

#     try:
#         # Ensure the browser window is in focus
#         driver.switch_to.window(driver.current_window_handle)
        
#         # Wait for the form to be populated by JavaScript
#         docket_number_input = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, 'docketbird-search-input')))
#         send_keyboard_input(driver, docket_number_input, docket_number)

#         # Click the search button
#         search_button = driver.find_element(By.ID, 'docketbird-search-icon')
#         move_mouse_to(driver, search_button)
#         search_button.click()
#         # random_delay()
#         print(f"Search button clicked for {docket_number}")


#         # Wait for the elements to be present and stable
#         results = wait_for_results_to_stabilize(driver)
#         print(f"Search results obtained for {docket_number}")

#         def score_link(element):
#             link_text = element.find_element(By.CSS_SELECTOR, '.search-result-case-name a.case_url').text.strip()
#             score = SequenceMatcher(None, link_text, title).ratio()
#             return score + 1 if "schedule" in link_text.lower() else score

#         best_match = max(results, key=score_link, default=None)
#         print(f"Best match found for {docket_number}")

#         if best_match:
#             link_element = best_match.find_element(By.CSS_SELECTOR, '.search-result-case-name a.case_url')
#             move_mouse_to(driver, link_element)
#             actions = ActionChains(driver)
#             actions.key_down(Keys.CONTROL).click(link_element).key_up(Keys.CONTROL).perform()  # on a new tab
#             print(f"Case {docket_number} clicked")
#             return True
#         else:
#             print(f"Case {docket_number} not found")
#             return False

#     except Exception as e:
#         log_message(f"Error finding case for {docket_number} in {court}: {e}")
#         error_msg = str(e)
#         try:
#             error_dir = save_browser_state(driver, error_msg, traceback.format_exc())
#             log_message(f"Error state saved to: {error_dir}", level='ERROR')
#         except Exception as save_error:
#             log_message(f"Failed to save error state: {save_error}", level='ERROR')
        
#         log_message(f"Traceback:\n{traceback.format_exc()}")
#         return False


# def wait_for_results_to_stabilize(driver, timeout=10, poll_frequency=1):
#     # First, wait for either search results container OR the no-results container
#     WebDriverWait(driver, timeout).until(
#         EC.presence_of_element_located((By.CSS_SELECTOR, '#search-results-cases-container, .results-list.search.noresults'))
#     )
    
#     # Check if we have a "no results" case first
#     no_results = driver.find_elements(By.CSS_SELECTOR, '.results-list.search.noresults')
#     if no_results:
#         return []
    
#     # If we have results, proceed with the stabilization check
#     end_time = time.time() + timeout
#     previous_count = 0
#     while time.time() < end_time:
#         elements = driver.find_elements(By.CSS_SELECTOR, '#search-results-cases-container div.search-result')
#         current_count = len(elements)
#         if current_count == previous_count and current_count > 0:
#             return elements
#         previous_count = current_count
#         time.sleep(poll_frequency)
#     return []