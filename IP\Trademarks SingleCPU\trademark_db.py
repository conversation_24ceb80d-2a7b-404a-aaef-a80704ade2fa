"""
Trademark Database Operations Module

This module provides functions to interact with the PostgreSQL database for trademark data.
"""

import os
import psycopg2
import psycopg2.extras
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("trademark_db.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_db_connection():
    """
    Create a connection to the PostgreSQL database.
    
    Returns:
        connection: PostgreSQL database connection
    """
    try:
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD"),
            dbname=os.getenv("POSTGRES_DB")
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}")
        raise

def upsert_trademarks(trademarks):
    """
    Insert or update trademark records in the database using UPSERT logic.
    
    Args:
        trademarks (list): List of trademark dictionaries to insert/update
        
    Returns:
        int: Number of records processed
    """    
    # Filter trademarks to keep only the last entry for each ser_no
    original_count = len(trademarks)
    logger.info(f"Received {original_count} trademarks for upsert.")
    
    filtered_trademarks = {}
    skipped_count = 0
    for trademark in trademarks:
        ser_no = trademark.get('ser_no')
        if ser_no:
            filtered_trademarks[ser_no] = trademark
        else:
            logger.warning(f"Skipping trademark due to missing 'ser_no': {trademark}")
            skipped_count += 1
            
    trademarks = list(filtered_trademarks.values())
    logger.info(f"Filtered trademarks: {len(trademarks)} remaining after removing duplicates/missing ser_no (skipped {skipped_count}).")

    # Check if the list is empty after filtering
    if not trademarks:
        logger.info("No trademarks left to upsert after filtering.")
        return 0
        
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Define the columns to update
        columns = [
            'reg_no', 'ser_no', 'TRO', 'applicant_name', 'mark_text', 'int_cls',
            'filing_date', 'nb_suits', 'country_codes', 'mark_current_status_code',
            'mark_feature_code', 'mark_standard_character_indicator',
            'mark_disclaimer_text_daily', 'mark_image_colour_statement_daily',
            'mark_translation_statement_daily', 'name_portrait_statement_daily',
            'mark_description_statement_daily', 'certification_mark_statement_daily',
            'lining_stippling_statement_daily', 'section_2f_statement_daily',
            'goods_services', 'goods_services_text_daily', 'case_file_statements_other', 'info_source'
        ]
        
        # Prepare the SQL statement for UPSERT
        placeholders = ', '.join(['%s'] * len(columns))
        column_names = ', '.join(columns)
        
        # Build the SET clause for the UPDATE part
        update_set = ', '.join([f"{col} = EXCLUDED.{col}" for col in columns if col != 'ser_no'])
        
        sql = f"""
        INSERT INTO trademarks ({column_names})
        VALUES ({placeholders})
        ON CONFLICT (ser_no) 
        DO UPDATE SET {update_set}
        """
        
        # Process trademarks in batches
        batch_size = 5000
        processed = 0
        
        for i in range(0, len(trademarks), batch_size):
            batch = trademarks[i:i+batch_size]
            batch_values = []
            
            for trademark in batch:
                # Set info_source
                trademark['info_source'] = trademark.pop('last_updated_source', 'daily_api')
                
                # Prepare values in the correct order
                row_values = [trademark.get(col) for col in columns]
                batch_values.append(row_values)
            
            # Execute batch insert
            psycopg2.extras.execute_batch(cursor, sql, batch_values)
            conn.commit()
            processed += len(batch)
            logger.info(f"Processed {processed}/{len(trademarks)} trademark records")
        
        return processed
    
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error upserting trademarks: {str(e)}")
        raise
    
    finally:
        if conn:
            conn.close()

def update_image_source(ser_no, image_source):
    """
    Update the image_source field for a trademark record.
    
    Args:
        ser_no (str): Serial number of the trademark
        image_source (str): Source of the image (e.g., 'USPTO_URL', 'NotFound')
        
    Returns:
        bool: True if successful, False otherwise
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        sql = """
        UPDATE trademarks
        SET image_source = %s
        WHERE ser_no = %s
        """
        
        cursor.execute(sql, (image_source, ser_no))
        conn.commit()
        
        return cursor.rowcount > 0
    
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Error updating image source for {ser_no}: {str(e)}")
        return False
    
    finally:
        if conn:
            conn.close()