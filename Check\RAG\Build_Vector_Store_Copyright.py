# import tensorflow as tf
# from tensorflow.keras import applications
# from tensorflow.keras.applications import ResNet50, VGG16

import os
import sys
from dotenv import load_dotenv

# Get the current working directory
current_directory = os.getcwd()

# Add the current directory to the Python path
sys.path.insert(0, current_directory)  # Use insert(0, ...) to prioritize it

load_dotenv()

import re
import json
import base64
import zlib
import numpy as np
import cv2
from tqdm import tqdm
from PIL import Image
from tqdm import tqdm
import json
import zlib
import base64
import imagehash
from DatabaseManagement.ImportExport import get_table_from_GZ
from Check.RAG.RAG_Inference import get_clipv2_embeddings, get_efficientnet_embeddings, get_google_multimodal_embeddings
from tf_keras import applications
import timm
import torch
import time


# Load all models once
start_time = time.time()
resnet50 = applications.ResNet50(weights='imagenet', include_top=False, pooling='avg')
vgg16 = applications.VGG16(weights='imagenet', include_top=False, pooling='avg')
vgg19 = applications.VGG19(weights='imagenet', include_top=False, pooling='avg')  # Deeper version of VGG16
effnetv2l = applications.EfficientNetV2L(weights='imagenet', include_top=False, pooling='avg')
inception_resnet = applications.InceptionResNetV2(weights='imagenet', include_top=False, pooling='avg')
xception = applications.Xception(weights='imagenet', include_top=False, pooling='avg')
convnext_xlarge = applications.ConvNeXtXLarge(weights='imagenet', include_top=False, pooling='avg')
resnet152v2 = applications.ResNet152V2(weights='imagenet', include_top=False, pooling='avg')  # Deeper ResNet variant
resnet_model = applications.ResNet50(weights='imagenet', include_top=False, pooling='avg')
vgg_model = applications.VGG16(weights='imagenet', include_top=False, pooling='avg')
sift = cv2.SIFT_create()
orb = cv2.ORB_create()

swin = timm.create_model('swin_large_patch4_window12_384', pretrained=True, num_classes=0)
end_time = time.time()
print(f"\033[91mTime taken to load models: {end_time - start_time:.1f} seconds\033[0m")

def get_resnet_embeddings(image_paths):
    """Generate embeddings using ResNet50"""
    imgs = [preprocess_image_resnet(img_path) for img_path in image_paths]
    return resnet_model.predict(np.array(imgs))

def get_vgg_embeddings(image_paths):
    """Generate embeddings using VGG16"""
    imgs = [preprocess_image_vgg(img_path) for img_path in image_paths]
    return vgg_model.predict(np.array(imgs))

def get_orb_features(image_path):
    """Extract ORB features with RANSAC verification"""
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    _, des = orb.detectAndCompute(img, None)
    return des if des is not None else np.zeros((1, 32), dtype=np.uint8)

def get_sift_features(image_path):
    """Extract SIFT features"""
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    _, des = sift.detectAndCompute(img, None)
    return des if des is not None else np.zeros((1, 128), dtype=np.float32)

def calculate_dhash(image_path):
    """Calculate difference hash for image"""
    with Image.open(image_path) as img:
        return imagehash.dhash(img).hash.flatten()

def preprocess_image_resnet(image_path):
    """Preprocess image for ResNet50"""
    img = Image.open(image_path).convert('RGB')
    img = img.resize((224, 224))
    return applications.resnet50.preprocess_input(np.array(img))

def preprocess_image_vgg(image_path):
    """Preprocess image for VGG16"""
    img = Image.open(image_path).convert('RGB')
    img = img.resize((224, 224))
    return applications.vgg16.preprocess_input(np.array(img))

def preprocess_effnetv2(img_path):
    """Preprocess for EfficientNetV2L (380x380 input)"""
    img = Image.open(img_path).convert('RGB')
    img = img.resize((380, 380))
    return applications.efficientnet_v2.preprocess_input(np.array(img))

def preprocess_inception_resnet(img_path):
    """Preprocess for InceptionResNetV2 (299x299 input)"""
    img = Image.open(img_path).convert('RGB')
    img = img.resize((299, 299))
    return applications.inception_resnet_v2.preprocess_input(np.array(img))

def preprocess_xception(img_path):
    """Preprocess for Xception (299x299 input)"""
    img = Image.open(img_path).convert('RGB')
    img = img.resize((299, 299))
    return applications.xception.preprocess_input(np.array(img))

def preprocess_convnext(img_path):
    """Preprocess for ConvNeXtXLarge (384x384 input)"""
    img = Image.open(img_path).convert('RGB')
    img = img.resize((384, 384))
    return applications.convnext.preprocess_input(np.array(img))

def get_effnetv2_embeddings(image_paths):
    imgs = [preprocess_effnetv2(p) for p in image_paths]
    return effnetv2l.predict(np.array(imgs))

def get_inception_resnet_embeddings(image_paths):
    imgs = [preprocess_inception_resnet(p) for p in image_paths]
    return inception_resnet.predict(np.array(imgs))

def get_xception_embeddings(image_paths):
    imgs = [preprocess_xception(p) for p in image_paths]
    return xception.predict(np.array(imgs))

def get_convnext_embeddings(image_paths):
    imgs = [preprocess_convnext(p) for p in image_paths]
    return convnext_xlarge.predict(np.array(imgs))

def get_resnet152v2_embeddings(image_path):
    return resnet152v2.predict(np.expand_dims(preprocess_image_resnet(image_path), axis=0))
            
def get_vgg19_embeddings(image_path):
    return vgg19.predict(np.expand_dims(preprocess_image_vgg(image_path), axis=0))

def get_swin_embedding(image_path):
    img = Image.open(image_path).convert('RGB')
    img = img.resize((384, 384))
    tensor = torch.tensor(np.array(img)).permute(2,0,1).float()/255.0
    return swin(tensor.unsqueeze(0)).detach().numpy()[0]

# Add Sobel preprocessing function
def sobel_preprocess(image_path):
    """Apply Sobel edge detection preprocessing"""
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    sobelx = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
    return np.hypot(sobelx, sobely).astype(np.uint8)

# Modified feature extraction with edge preprocessing
def get_enhanced_sift_features(image_path):
    """Extract SIFT features from edge-enhanced image"""
    edges = sobel_preprocess(image_path)
    _, des = sift.detectAndCompute(edges, None)
    return des if des is not None else np.zeros((1, 128), dtype=np.float32)

def get_enhanced_orb_features(image_path):
    """Extract ORB features from edge-enhanced image"""
    edges = sobel_preprocess(image_path)
    _, des = orb.detectAndCompute(edges, None)
    return des if des is not None else np.zeros((1, 32), dtype=np.uint8)

def build_multi_model_embeddings_dataset(image_dir):
    # Define expected model/feature fields (update this when adding new models)
    expected_fields = {
        'efficientnet_embedding', 'clipv2_embedding', 'resnet_embedding',
        'vgg_embedding', 'dhash', 'orb_features', 'sift_features', 'orb_edges',
        'sift_edges', 'effnetv2_embedding', 'inception_resnet_embedding',
        'xception_embedding', 'convnext_embedding', 'resnet152v2_embedding',
        'vgg19_embedding', 'swin_embedding', 'google_multimodal_embedding',
        'reg_no', 'filename', 'full_filename', 'plaintiff_name', 'docket',
        'number_of_cases'
    }

    # Check existing data compatibility
    final_path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsMultiModel.npy")
    existing_data = None
    needs_reprocessing = False
    
    if os.path.exists(final_path):
        existing_data = np.load(final_path)
        existing_fields = set(existing_data.dtype.names)
        
        # Check if existing data has all current fields
        if existing_fields != expected_fields:
            print("Model/field mismatch detected. Existing data will be updated with new models/features.")
            needs_reprocessing = True
        else:
            print(f"Found compatible dataset with {len(existing_data)} entries")

    # Process all images if model mismatch, else only new images
    all_image_files = [f for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f))]
    
    existing_filenames = set()
    if existing_data is not None and not needs_reprocessing:
        existing_filenames = set(existing_data['filename'])
        image_files = [f for f in all_image_files if f not in existing_filenames]
    elif existing_data is not None:
        existing_filenames = set(existing_data['filename'])
        image_files = all_image_files
    else:
        image_files = all_image_files

    if not image_files:
        print("No new images to process")
        return

    # List to store case info
    reg_nos = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    dockets = []
    number_of_cases_list = []

    cases_df = get_table_from_GZ("tb_case")
    plaintiffs_df = get_table_from_GZ('tb_plaintiff')

    # Embedding lists
    google_multimodal_embs = []
    efficientnet_embs = []
    clipv2_embs = []
    resnet_embs = []
    vgg_embs = []
    dhashes = []
    orb_features = []
    sift_features = []
    effnetv2_embs = []
    inception_resnet_embs = []
    xception_embs = []
    convnext_embs = []
    resnet152v2_embs = []
    vgg19_embs = []
    swin_embs = []
    sift_edges_features = []
    orb_edges_features = []

    # Initialize chunk counter
    start_chunk_counter = 0
    chunk_counter = start_chunk_counter
    image_files = image_files[start_chunk_counter:]
    chunk_size = 100
    for img_file in tqdm(image_files, desc="Processing images"):
        try:
            start_time = time.time()
            img_path = os.path.join(image_dir, img_file)
            
            ### Case related info
            match = re.search(r'_(\d_\d+cv\d+)_', img_file)
            if match:
                docket = match.group(1).replace("cv", "-cv-").replace("_", ":")
            else:
                match = re.search(r'_(\d_\d+-cv-\d+)_', img_file)
                if match:
                    docket = match.group(1).replace("_", ":")
                else:
                    print(f"No docket found for {img_file}")
                    continue
            while len(docket) < 13: # add missing 0 if any
                docket = docket.replace("-cv-", "-cv-0")

            
            plaintiff_id = cases_df[cases_df['docket'] == docket]['plaintiff_id'].values[0]
            if plaintiff_id == 9:
                continue
        
            reg_no = cases_df[cases_df['docket'] == docket]['images'].values[0]['copyrights'][img_file]["reg_no"]
            reg_nos.append(reg_no)

            full_filename = cases_df[cases_df['docket'] == docket]['images'].values[0]['copyrights'][img_file]["full_filename"][0]
            full_filenames.append(full_filename)
            
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            plaintiff_names.append(plaintiff_name)

            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
            number_of_cases_list.append(number_of_cases)

            filenames.append(img_file)
            dockets.append(docket)
            
            ### Embeddings generation
            # Generate all embeddings

            def create_or_get(existing_data, existing_filenames, img_file, field_name, get_func, args):
                if existing_data is not None and img_file in existing_filenames and field_name in existing_data.dtype.names:
                    result = existing_data[existing_data['filename'] == img_file][field_name]
                else: 
                    result = get_func(*args)
    
                # Handle batch-model responses (assuming batch size 1)
                if isinstance(result, (list, np.ndarray)) and len(result) == 1:
                    return result[0]
                return result

            google_multimodal_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'google_multimodal_embedding', get_google_multimodal_embeddings, [[img_path]]))
            
            efficientnet_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'efficientnet_embedding', get_efficientnet_embeddings, [[img_path]]))
            clipv2_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'clipv2_embedding', get_clipv2_embeddings, [[img_path], "image"]))
            resnet_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'resnet_embedding', get_resnet_embeddings, [[img_path]]))
            vgg_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'vgg_embedding', get_vgg_embeddings, [[img_path]]))
            effnetv2_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'effnetv2_embedding', get_effnetv2_embeddings, [[img_path]]))
            inception_resnet_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'inception_resnet_embedding', get_inception_resnet_embeddings, [[img_path]]))
            xception_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'xception_embedding', get_xception_embeddings, [[img_path]]))
            convnext_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'convnext_embedding', get_convnext_embeddings, [[img_path]]))


            dhashes.append(create_or_get(existing_data, existing_filenames, img_file, 'dhash', calculate_dhash, [img_path]))
            
            # Feature extraction - Not saved in the array
            orb_des = create_or_get(existing_data, existing_filenames, img_file, 'orb_des', get_orb_features, [img_path])
            sift_des = create_or_get(existing_data, existing_filenames, img_file, 'sift_des', get_sift_features, [img_path])
            
            # New edge-enhanced features - Not saved in the array
            enhanced_orb_des = create_or_get(existing_data, existing_filenames, img_file, 'orb_edges_des', get_enhanced_orb_features, [img_path])
            enhanced_sift_des = create_or_get(existing_data, existing_filenames, img_file, 'sift_edges_des', get_enhanced_sift_features, [img_path])
            
            # Store both versions
            orb_features.append(orb_des[:100] if orb_des.shape[0] >= 100 else np.pad(orb_des, ((0,100-orb_des.shape[0]),(0,0))))
            sift_features.append(sift_des[:100] if sift_des.shape[0] >= 100 else np.pad(sift_des, ((0,100-sift_des.shape[0]),(0,0))))
                               
            orb_edges_features.append(enhanced_orb_des[:100] if enhanced_orb_des.shape[0] >= 100 else np.pad(enhanced_orb_des, ((0,100-enhanced_orb_des.shape[0]),(0,0))))
            sift_edges_features.append(enhanced_sift_des[:100] if enhanced_sift_des.shape[0] >= 100 else np.pad(enhanced_sift_des, ((0,100-enhanced_sift_des.shape[0]),(0,0))))


            resnet152v2_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'resnet152v2_embedding', get_resnet152v2_embeddings, [img_path]))
            vgg19_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'vgg19_embedding', get_vgg19_embeddings, [img_path]))
            swin_embs.append(create_or_get(existing_data, existing_filenames, img_file, 'swin_embedding', get_swin_embedding, [img_path]))


            end_time = time.time()
            print(f"\033[91mTime taken to generate 1 image embeddings: {end_time - start_time:.1f} seconds\033[0m")

            # Save chunk every 100 images
            chunk_counter += 1
            if chunk_counter % chunk_size == 0 or img_file == image_files[-1]:
                # Create temporary structured array
                temp_dtype = [
                    # Embeddings
                    ('google_multimodal_embedding', 'float32', (len(google_multimodal_embs[0]),)),
                    ('efficientnet_embedding', 'float32', (len(efficientnet_embs[0]),)),
                    ('clipv2_embedding', 'float32', (len(clipv2_embs[0]),)),
                    ('resnet_embedding', 'float32', (len(resnet_embs[0]),)),
                    ('vgg_embedding', 'float32', (len(vgg_embs[0]),)),
                    ('dhash', 'uint8', (len(dhashes[0]),)),
                    ('orb_features', 'uint8', (len(orb_features[0]), 32)),
                    ('sift_features', 'float32', (len(sift_features[0]), 128)),
                    ('orb_edges', 'uint8', (len(orb_edges_features[0]), 32)),  # New edge-enhanced features
                    ('sift_edges', 'float32', (len(sift_edges_features[0]), 128)),
                    ('effnetv2_embedding', 'float32', (len(effnetv2_embs[0]),)),
                    ('inception_resnet_embedding', 'float32', (len(inception_resnet_embs[0]),)),
                    ('xception_embedding', 'float32', (len(xception_embs[0]),)),
                    ('convnext_embedding', 'float32', (len(convnext_embs[0]),)),
                    ('resnet152v2_embedding', 'float32', (len(resnet152v2_embs[0]),)),
                    ('vgg19_embedding', 'float32', (len(vgg19_embs[0]),)),
                    ('swin_embedding', 'float32', (len(swin_embs[0]),)),
                    # Case info
                    ('reg_no', 'U100'),
                    ('filename', 'U100'),
                    ('full_filename', 'U100'),
                    ('plaintiff_name', 'U100'),
                    ('docket', 'U100'),
                    ('number_of_cases', 'int')
                ]
                
                
                # Calculate start/end indices for this chunk
                start_idx = max(0, chunk_counter - chunk_size - start_chunk_counter)
                end_idx = len(google_multimodal_embs)

                temp_array = np.zeros(end_idx - start_idx + 1, dtype=temp_dtype)
                
                # Fill temporary array
                for i in range(start_idx, end_idx):
                    temp_array[i-start_idx] = (
                        google_multimodal_embs[i],
                        efficientnet_embs[i],
                        clipv2_embs[i],
                        resnet_embs[i],
                        vgg_embs[i],
                        dhashes[i],
                        orb_features[i],
                        sift_features[i],
                        orb_edges_features[i],
                        sift_edges_features[i],
                        effnetv2_embs[i],
                        inception_resnet_embs[i],
                        xception_embs[i],
                        convnext_embs[i],
                        resnet152v2_embs[i],
                        vgg19_embs[i],
                        swin_embs[i],
                        reg_nos[i],
                        filenames[i],
                        full_filenames[i],
                        plaintiff_names[i],
                        dockets[i],
                        number_of_cases_list[i]
                    )
                
                # Save chunk
                chunk_path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', 
                                       f"EmbeddingsMultiModel_chunk_{chunk_counter//chunk_size}.npy")
                np.save(chunk_path, temp_array)
                print(f"\033[92mSaved chunk {chunk_counter//chunk_size}\033[0m")

        except Exception as e:
            print(f"Error processing {img_file}: {e}")

    # After processing all images
    concatenate_all_chunks(existing_data, needs_reprocessing=needs_reprocessing)


def concatenate_all_chunks(existing_data=None, needs_reprocessing=False):
    chunk_files = sorted([f for f in os.listdir(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors')) 
                        if f.startswith("EmbeddingsMultiModel_chunk_")])
    
    if not chunk_files:
        return np.array([])  # No new data
    
    new_data = np.concatenate([np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', f)) 
                                for f in chunk_files])
    
    if existing_data is not None and not needs_reprocessing:
        full_array = np.concatenate([existing_data, new_data])
    else:
        full_array = new_data
    
    # Save final combined array
    np.save(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsMultiModel.npy"), full_array)
    print(f"Saved updated dataset with {len(full_array)} entries")
    
    # Cleanup chunk files
    for f in chunk_files:
        os.remove(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', f))
    
    return full_array


def print_docket_numbers(npy_file_path):
    """Loads the npy file and iterates through its content, printing the docket number."""
    structured_array = np.load(npy_file_path)

    for record in structured_array:
        print(record['docket'])

if __name__ == "__main__":
    # concatenate_all_chunks()
    build_multi_model_embeddings_dataset(os.path.join(os.getcwd(), 'data', 'IP', "Copyright", "Production"))
    # print_docket_numbers(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsMultiModel.npy"))
    # print_docket_numbers(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsMultiModel Without Google.npy"))
