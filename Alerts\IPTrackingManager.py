from typing import Dict, Any, Optional, Tu<PERSON>, Set, List
# Add import for cleaning patent numbers
import pandas as pd # Added import for pandas
import copy


class IPTrackingManager:
    """Manages the state and logic for tracking IP evidence findings."""

    def __init__(self, nos_description_text: str = "", initial_state: Optional[Dict] = None):
        """
        Initializes the IP tracking state based on the case description or from a saved state.

        Args:
            nos_description_text: The description string from the case data.
            initial_state: Optional dictionary to initialize the state from.
        """
        self.iptypes = ['trademark', 'copyright', 'patent']
        if initial_state:
            # Deep copy the initial state to avoid modifying the original
            self._state = copy.deepcopy(initial_state)

            # Ensure IP type dictionaries exist
            for ip_type in self.iptypes:
                if ip_type not in self._state:
                    self._state[ip_type] = {'is_relevant': False, 'goal_met': True, 'reg_nos_status': 'pending'}

            # Ensure set-based fields are sets for each IP type
            for ip_type in self.iptypes:
                ip_type_state = self._state.get(ip_type, {})
                for key_to_be_set in ['target_reg_nos', 'non_target_reg_nos', 'found_reg_nos',
                                     'target_attachment_indices', 'target_step_nbs', 'processed_locations']:
                    if key_to_be_set in ip_type_state and isinstance(ip_type_state[key_to_be_set], list):
                        ip_type_state[key_to_be_set] = set(ip_type_state[key_to_be_set])
                    elif key_to_be_set not in ip_type_state:
                        ip_type_state[key_to_be_set] = set()

            # Convert copyright pictures_not_found back to DataFrame: Because Pandas DataFrames are not directly JSON serializable, the export_state method converts the DataFrame into a dictionary.
            if 'copyright' in self._state and 'reg_numbers_pictures_not_found' in self._state['copyright']:
                pictures_not_found = self._state['copyright']['reg_numbers_pictures_not_found']
                if isinstance(pictures_not_found, dict):  # If it was serialized as a dict
                    try:
                        self._state['copyright']['reg_numbers_pictures_not_found'] = pd.DataFrame.from_dict(pictures_not_found.get('data', []))
                    except Exception:
                        # If conversion fails, initialize a new DataFrame
                        self._state['copyright']['reg_numbers_pictures_not_found'] = pd.DataFrame(columns=["reg_no", "Copyright Claimant", "Title", "best_google_image"])
            elif 'copyright' in self._state and 'reg_numbers_pictures_not_found' not in self._state['copyright']:
                self._state['copyright']['reg_numbers_pictures_not_found'] = pd.DataFrame(columns=["reg_no", "Copyright Claimant", "Title", "best_google_image"])
            

            print(f"IPTrackingManager initialized from saved state.")
        else:
            # Initialize fresh state
            self._state: Dict[str, Any] = {
                "trademark": {},
                "copyright": {},
                "patent": {}
            }
            self._initialize_state(nos_description_text)

    def _initialize_state(self, case_nos_description: str):
        """Helper method to initialize the state for each IP type."""
        nos_desc_lower = case_nos_description.lower() if case_nos_description else ""

        for ip_type in self.iptypes:
            is_relevant = ip_type in nos_desc_lower
            initial_pictures_not_found: Any # Define type hint for clarity
            if ip_type == 'copyright':
                # Initialize as DataFrame for copyright
                initial_pictures_not_found = pd.DataFrame(columns=["reg_no", "Copyright Claimant", "Title", "best_google_image"])
            else:
                # Keep as list for other types (or adjust if needed elsewhere)
                initial_pictures_not_found = []

            self._state[ip_type] = {
                "is_relevant": is_relevant,
                "goal_met": not is_relevant,  # Goal is met if not relevant
                "target_reg_nos": set(), # For VA copyrights, or all for TM/PT
                "non_target_reg_nos": set(), # For non-VA copyrights
                "found_reg_nos": set(),
                "reg_nos_status": "pending", # pending, found, not_found
                "target_main_doc": False,
                "target_attachment_indices": [],
                "target_step_nbs": [],
                "processed_locations": set(), # Set of location_id strings (e.g., "att_1", "step_5")
                "reg_numbers_pictures_not_found": initial_pictures_not_found # Use initialized structure
            }

    def update_values_from_llm(self, llm_analysis_result: dict):
        """
        Updates state values based on LLM analysis results for each IP type.
        Applies normalization to patent registration numbers. # <-- Added note

        This includes updating the 'is_relevant' flag based on 'owned_by_plaintiff',
        adding to the set of 'target_reg_nos', and replacing other target fields.

        Args:
            llm_analysis_result: The structured dictionary from LLM analysis.
                                 Expected keys: 'trademark', 'copyright', 'patent'.
                                 Each sub-dict can contain 'owned_by_plaintiff', 'target_reg_nos',
                                 'reg_nos_status', 'target_attachment_indices', 'target_step_nbs'.
        """
        for ip_type, data in llm_analysis_result.items():
            if ip_type in self._state: # Process if the IP type exists in our state
                state_entry = self._state[ip_type]

                # Update relevance based on LLM output
                llm_is_relevant = data.get("owned_by_plaintiff", False)
                if llm_is_relevant:
                    state_entry["is_relevant"] = True # Only set to True, never False

                # Update target registration numbers using set union
                llm_reg_nos = data.get("registration_numbers", [])
                if ip_type == "copyright":
                    for rn in llm_reg_nos:
                        # Add to appropriate target/non-target set
                        if rn.startswith("VA"):
                            state_entry["target_reg_nos"].add(rn)
                            # All LLM-identified copyrights are initially added to pictures_not_found
                            self.add_picture_not_found(ip_type, rn, "", "") # Initialize in DataFrame
                        else:
                            state_entry["non_target_reg_nos"].add(rn)
                else:
                    state_entry["target_reg_nos"].update(llm_reg_nos)


                # Update other fields (replace existing values)
                state_entry["reg_nos_status"] = data.get("registration_numbers_status", state_entry["reg_nos_status"])
                state_entry["target_main_doc"] = data.get("target_main_doc", state_entry["target_main_doc"])
                state_entry["target_attachment_indices"] = data.get("target_attachment_indices", [])  # already sorted and removed dupes in IPAnalysisLLM
                state_entry["target_step_nbs"] = data.get("target_step_nbs", []) # already sorted and removed dupes in IPAnalysisLLM

                # Re-evaluate goal status if relevance or targets might have changed
                self._set_goal_status(ip_type)


    def record_finding(self, ip_type: str, location_id: str, found_reg_nos: Optional[List[str]] = None):
        """
        Records findings for a specific IP type at a given location.

        Args:
            ip_type: The type of IP ('trademark', 'copyright', 'patent').
            location_id: Identifier for the location processed (e.g., "att_1", "step_5").
            found_reg_nos: A list of registration numbers found at this location.
        """
        if ip_type not in self._state or not self._state[ip_type]["is_relevant"]:
            return # Ignore findings for non-relevant IP types

        state_entry = self._state[ip_type]
        state_entry["processed_locations"].add(location_id)

        if found_reg_nos:
            state_entry["found_reg_nos"].update(found_reg_nos)
            if ip_type == "copyright":
                # Remove from reg_numbers_pictures_not_found DataFrame
                df = state_entry["reg_numbers_pictures_not_found"]
                if isinstance(df, pd.DataFrame):
                    state_entry["reg_numbers_pictures_not_found"] = df[~df['reg_no'].isin(found_reg_nos)]

        self._set_goal_status(ip_type)


    def _set_goal_status(self, ip_type: str):
        """
        Checks if the goal for the specified IP type has been met based on findings.
        Updates the 'goal_met' flag in the state.
        """

        state_entry = self._state[ip_type] # type: ignore

        if ip_type == "copyright":
            if state_entry["target_reg_nos"]: # target_reg_nos for copyright now only contains "VA" numbers
                # Goal is met if all targeted "VA" numbers are found
                state_entry["goal_met"] = state_entry["target_reg_nos"].issubset(state_entry["found_reg_nos"])
            elif state_entry["non_target_reg_nos"]: # No "VA" numbers were targeted, but copyright is relevant because of non_target_reg_nos which have been identified => we are done
                state_entry["goal_met"] = True
        else: # For Patent and Trademark
            if state_entry["target_reg_nos"]:
                all_targets_found = state_entry["target_reg_nos"].issubset(state_entry["found_reg_nos"])
                state_entry["goal_met"] = all_targets_found
            else:  # if IP is relevant, but there are no target_reg_nos, then all we need is to find any IP for the goal to be met
                any_ip_found = bool(state_entry["found_reg_nos"])
                state_entry["goal_met"] = any_ip_found


    def add_picture_not_found(self, ip_type: str, reg_no: str, claimant: str, title: str, location_id: str = None):
        """Adds or updates details for a copyright registration number whose picture was not found in the DataFrame."""
        if ip_type == 'copyright' and ip_type in self._state:
            df = self._state[ip_type]["reg_numbers_pictures_not_found"]
            # Check if reg_no already exists
            if reg_no in df['reg_no'].values:
                # Update existing row
                df.loc[df['reg_no'] == reg_no, ['Copyright Claimant', 'Title']] = [claimant, title]
            else:
                # Append new row
                new_row = pd.DataFrame([{
                    "reg_no": reg_no,
                    "Copyright Claimant": claimant,
                    "Title": title,
                    "best_google_image": None # Initialize best_google_image as None
                }])
                self._state[ip_type]["reg_numbers_pictures_not_found"] = pd.concat([df, new_row], ignore_index=True)
                
            if location_id:
                self._state[ip_type]["processed_locations"].add(location_id)
                
        elif ip_type in self._state:
             # Handle non-copyright types if necessary (currently keeps old list logic if structure wasn't changed)
             # For now, assume this method is primarily for copyrights post-refactor
             # If other types need similar logic, adjust _initialize_state and this method
             pass # Or add logging/error handling if called unexpectedly for other types

    def get_pictures_not_found(self, ip_type: str) -> Any: # Return type depends on ip_type now
        """Gets the structure (DataFrame for copyright, list otherwise) storing details of missing pictures."""
        if ip_type in self._state:
            return self._state[ip_type]["reg_numbers_pictures_not_found"]
        # Return appropriate empty structure if ip_type not found
        return pd.DataFrame(columns=["reg_no", "Copyright Claimant", "Title", "best_google_image"]) if ip_type == 'copyright' else []

    def clear_pictures_not_found(self, ip_type: str):
        """Clears the structure storing details of missing pictures for a given IP type."""
        if ip_type in self._state:
            if ip_type == 'copyright':
                # Reset to an empty DataFrame for copyright
                self._state[ip_type]["reg_numbers_pictures_not_found"] = pd.DataFrame(columns=["reg_no", "Copyright Claimant", "Title", "best_google_image"])
            else:
                # Reset to an empty list for other types
                self._state[ip_type]["reg_numbers_pictures_not_found"] = []

    def is_goal_met(self, ip_type: str) -> bool:
        """
        Returns the goal status for the specified IP type.

        Args:
            ip_type: The type of IP ('trademark', 'copyright', 'patent').

        Returns:
            True if the goal for the IP type is met, False otherwise.
        """
        return self._state.get(ip_type, {}).get("goal_met", True) # Default to True if type invalid/not relevant

    def is_goal_relevant(self, ip_type: str) -> bool:
        """
        Returns the relevance status for the specified IP type.

        Args:
            ip_type: The type of IP ('trademark', 'copyright', 'patent').

        Returns:
            True if the IP type is relevant, False otherwise.
        """
        return self._state.get(ip_type, {}).get("is_relevant", False)

    def are_all_relevant_goals_met(self) -> bool:
        """
        Checks if the goals for all relevant IP types have been met.

        Returns:
            True if all relevant goals are met, False otherwise.
        """
        for ip_type, state_entry in self._state.items():
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                return False
        return True
    
    def are_all_step_processing_goals_met(self) -> bool:
        """
        Checks if goals that require step-by-step document processing (like exhibits) are met.
        For Copyright, this means all 'target_reg_nos' (VA numbers from LLM) have been
        either fully processed (artwork found in `found_reg_nos`) or their certificate was found 
        in an exhibit / LLM identified them (and they are thus in `reg_numbers_pictures_not_found` 
        awaiting Google/other search).
        For TM/PT, this is the same as `are_all_relevant_goals_met` if only considering `found_reg_nos`.
        """
        for ip_type, state_entry in self._state.items(): # type: ignore
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                if ip_type == 'copyright':
                    # For copyright, step processing (exhibits) is considered done for VA numbers if:
                    # 1. Their artwork has been obtained (in found_reg_nos) OR
                    # 2. They are listed in reg_numbers_pictures_not_found (meaning their cert was found in an exhibit,
                    #    or they were LLM-identified, and are now awaiting Google/USCO/main_pdf search).
                    pictures_not_found_df = state_entry["reg_numbers_pictures_not_found"]
                    reg_nos_in_pictures_not_found_df = set(pictures_not_found_df['reg_no'].tolist())
                    
                    if not state_entry["target_reg_nos"].issubset(state_entry["found_reg_nos"] | reg_nos_in_pictures_not_found_df):
                        return False # Copyright step processing still needed for some VA numbers
                else: # For TM/PT, if goal_met is false (based on found_reg_nos), step processing is not done.
                    return False
        return True

    def get_missing_status(self) -> str:
        """
        Returns a sentence explaining what is still missing for unmet goals.
        """
        missing_statuses = []
        for ip_type, state_entry in self._state.items():
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                if "target_reg_nos" in state_entry and state_entry["target_reg_nos"]:
                    missing_reg_nos = state_entry["target_reg_nos"] - state_entry.get("found_reg_nos", set())
                    if missing_reg_nos:
                        missing_statuses.append(f"Looking for {ip_type.capitalize()} Reg No: {', '.join(sorted(list(missing_reg_nos)))}.")
                else:
                    missing_statuses.append(f"Looking for unknown {ip_type.capitalize()} reg numbers.")

        if not missing_statuses:
            return "Nothing Missing." # Return empty string if all relevant goals are met

        # Join statuses into a nice sentence
        return " ".join(missing_statuses)

    def get_targeted_locations_for_unmet_goals(self) -> Tuple[Set[int], Set[int]]:
        """
        Gets the set of targeted attachment indices and step numbers for IP types
        whose goals are not yet met.

        Returns:
            A tuple containing two sets: (attachment_indices, step_numbers).
        """
        unmet_attachment_indices: Set[int] = set()
        unmet_step_nbs: Set[int] = set()

        for ip_type, state_entry in self._state.items():
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                unmet_attachment_indices.update(state_entry.get("target_attachment_indices", set()))
                unmet_step_nbs.update(state_entry.get("target_step_nbs", set()))

        return sorted(list(unmet_attachment_indices)), sorted(list(unmet_step_nbs))

    def export_state(self) -> Dict[str, Any]:
        """
        Returns an optimized, serializable copy of the internal state dictionary.
        Removes empty collections and unnecessary fields to minimize JSON size.
        Converts sets to sorted lists for JSON compatibility.

        Returns:
            A dictionary representing the minimal tracking state, ready for serialization.
        """
        # Create a new state dictionary with only essential information
        optimized_state = {}

        # Process each IP type
        for ip_type in self.iptypes:
            if ip_type in self._state:
                ip_type_state = self._state[ip_type]

                # Skip IP types that aren't relevant
                if not ip_type_state.get('is_relevant', False):
                    continue

                # Start with essential fields
                optimized_ip_state = {
                    'is_relevant': True,
                    'goal_met': ip_type_state.get('goal_met', False),
                    'reg_nos_status': ip_type_state.get('reg_nos_status', 'Unknown')
                }

                # Include non-empty collections that are important for resuming
                for key in ['target_reg_nos', 'found_reg_nos', 'non_target_reg_nos']:
                    if key in ip_type_state and ip_type_state[key]:
                        optimized_ip_state[key] = sorted(list(ip_type_state[key]))

                # For copyright, include pictures_not_found only if it has data
                if ip_type == 'copyright' and 'reg_numbers_pictures_not_found' in ip_type_state:
                    pictures_not_found = ip_type_state['reg_numbers_pictures_not_found']
                    if isinstance(pictures_not_found, pd.DataFrame) and not pictures_not_found.empty:
                        optimized_ip_state['reg_numbers_pictures_not_found'] = {
                            'data': pictures_not_found.to_dict(orient='records')
                        }

                # Add the optimized IP state to the main state
                optimized_state[ip_type] = optimized_ip_state

        return optimized_state

    def get_final_state(self) -> Dict[str, Dict[str, Any]]:
        """
        Returns a copy of the internal state dictionary.

        Note: This method is maintained for backward compatibility.
        For serialization, use export_state() instead.

        Returns:
            A dictionary representing the final tracking state.
        """
        # Return a deep copy might be safer if mutable objects (like sets) are modified externally
        # For now, a shallow copy is sufficient as sets are typically replaced, not mutated externally.
        return self._state.copy()

    def clear_ip_manager_state_for_full_reprocess(self, nos_description_text: str = ""):
        """
        Resets the IP manager state for a full reprocess.
        Clears all state.

        Args:
            nos_description_text: The description string to use for re-initializing relevance.
        """
        # Reset to a fresh state
        self._state = {
            "trademark": {},
            "copyright": {},
            "patent": {}
        }
        # Re-initialize the state based on the description
        self._initialize_state(nos_description_text)