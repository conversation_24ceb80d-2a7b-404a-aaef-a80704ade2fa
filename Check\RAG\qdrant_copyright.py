"""
Qdrant-based implementation for copyright similarity search.
This module provides functions to find similar copyrights using Qdrant vector database.
"""

import os
import aiohttp
from typing import List, Dict, Any
from dotenv import load_dotenv
from langfuse.decorators import observe

# Import the embedding functions from RAG_Inference
from Check.RAG.RAG_Inference import get_efficientnet_embeddings, get_clipv2_embeddings

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_URL = os.getenv("QDRANT_URL", "https://qdrant1.maidalv.com")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", "2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ")
API_BEARER_TOKEN = os.getenv("API_BEARER_TOKEN", "2WFupSDRbBCCqz4L6o31unBJX5fVtIIJ")

@observe()
async def find_most_similar_copyrights_qdrant(
    query_image_paths: List[str],
    check_id: str,
    client_id: str,
    plaintiff_df=None,
    top_n: int = 1,
    similarity_threshold: float = 0.8
) -> List[Dict[str, Any]]:
    """
    Find the most similar copyrights using Qdrant vector database.

    Args:
        query_image_paths (List[str]): Paths to the query images.
        check_id (str): The identifier for this specific check/batch.
        client_id (str): The identifier for the client submitting the batch.
        plaintiff_df: DataFrame containing plaintiff information for ID to name conversion.
        top_n (int): Number of top similar images to return.
        similarity_threshold (float): Minimum cosine similarity score to consider a match.

    Returns:
        List[Dict[str, Any]]: List of match information for the top matches.
    """
    if len(query_image_paths) == 0:
        return []

    # Generate embeddings for query images
    efficientnet_embeddings = get_efficientnet_embeddings(query_image_paths)
    clipv2_embeddings = get_clipv2_embeddings(query_image_paths, "image")

    # Prepare the request payload for the forward_check endpoint
    products = []
    for i, path in enumerate(query_image_paths):
        products.append({
            "id": f"{os.path.basename(path)}",
            "image_clip": clipv2_embeddings[i].tolist(),
            "image_efficientnet": efficientnet_embeddings[i].tolist()
        })

    payload = {
        "client_id": client_id,
        "check_id": check_id,
        "products": products
    }

    # Make the API request to the forward_check endpoint
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{QDRANT_URL}/forward_check",
            json=payload,
            headers={
                "Authorization": f"Bearer {API_BEARER_TOKEN}",
                "Content-Type": "application/json"
            }
        ) as response:
            if response.status != 200:
                print(f"Error from Qdrant API: {response.status}")
                return []

            result = await response.json()

    # Process the results
    all_matches = []

    for product_result in result.get("results", []):
        product_id = product_result.get("input_product_id")
        query_image_path = next((path for path in query_image_paths if os.path.basename(path) == product_id), None)

        if not query_image_path:
            continue

        for infringement in product_result.get("potential_infringements", []):
            if infringement.get("ip_type") != "Copyright":
                continue

            metadata = infringement.get("metadata", {})

            # Convert plaintiff_ids to plaintiff_name if plaintiff_df is provided
            plaintiff_name = ""
            if plaintiff_df is not None and "plaintiff_ids" in metadata:
                plaintiff_ids = metadata.get("plaintiff_ids", [])
                if plaintiff_ids and len(plaintiff_ids) > 0:
                    plaintiff_id = plaintiff_ids[0]
                    plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == plaintiff_id].empty else ""

            # Build filename and full_filename based on registration_number
            reg_no = metadata.get("registration_number", "")
            # In a real implementation, you would need to implement the specific logic for building filenames
            # For now, we'll use placeholders that will cause errors to be noticed
            filename = f"COPYRIGHT_{reg_no}_FILENAME_NEEDS_IMPLEMENTATION"
            full_filename = f"COPYRIGHT_{reg_no}_FULL_FILENAME_NEEDS_IMPLEMENTATION"

            match_info = {
                "query_image_path": query_image_path,
                "filename": filename,
                "full_filename": full_filename,
                "reg_no": reg_no,
                "similarity": f"{infringement.get('score', 0):.2f}",
                "plaintiff_name": plaintiff_name or metadata.get("copyright_claimant", ""),
                "docket": metadata.get("TRO", ""),
                "number_of_cases": len(metadata.get("plaintiff_ids", [])) if "plaintiff_ids" in metadata else 1,
                "approach": "qdrant"
            }

            all_matches.append(match_info)

    # Sort all matches by similarity
    all_matches.sort(key=lambda x: float(x["similarity"]), reverse=True)

    # Apply filtering based on similarity threshold
    filtered_matches = [match for match in all_matches if float(match["similarity"]) >= similarity_threshold]

    # Limit to top_n results
    return filtered_matches[:top_n]
