"""
Reverse check endpoint for comparing IP assets against product images.
"""

import psycopg2
from fastapi import APIRouter, Depends
from qdrant_client.http import models
from typing import List, Dict, Any

from ..models.schemas import ReverseCheckRequest, ReverseCheckResponse, IPAssetInfringement, ProductPointInfringement
from ..utils.auth import verify_token
from ..utils.db import get_db_connection
from ..services.qdrant_service import upsert_ip_assets, query_batch_points
from ..services.similarity_service import apply_similarity_filtering

router = APIRouter()

@router.post("/reverse_check", response_model=ReverseCheckResponse, dependencies=[Depends(verify_token)])
async def reverse_check(request: ReverseCheckRequest):
    """
    Reverse Check endpoint.
    Compare newly added IP assets against the database of user-submitted product images.
    
    Args:
        request: The reverse check request.
        
    Returns:
        The reverse check response.
    """
    # Prepare IP assets for upsertion into PostgreSQL
    conn = get_db_connection()
    cursor = conn.cursor()
    
    for ip_asset in request.ip_assets:
        if ip_asset.ip_type == "Copyright":
            # Insert into copyrights table
            columns = ", ".join(ip_asset.metadata.keys())
            placeholders = ", ".join(["%s"] * len(ip_asset.metadata))
            values = list(ip_asset.metadata.values())
            
            cursor.execute(
                f"INSERT INTO copyrights (id, {columns}) VALUES (%s, {placeholders}) ON CONFLICT (id) DO UPDATE SET {', '.join([f'{k} = EXCLUDED.{k}' for k in ip_asset.metadata.keys()])}",
                [ip_asset.id] + values
            )
        elif ip_asset.ip_type == "Patent":
            # Insert into patents table
            columns = ", ".join(ip_asset.metadata.keys())
            placeholders = ", ".join(["%s"] * len(ip_asset.metadata))
            values = list(ip_asset.metadata.values())
            
            cursor.execute(
                f"INSERT INTO patents (id, {columns}) VALUES (%s, {placeholders}) ON CONFLICT (id) DO UPDATE SET {', '.join([f'{k} = EXCLUDED.{k}' for k in ip_asset.metadata.keys()])}",
                [ip_asset.id] + values
            )
        elif ip_asset.ip_type == "Trademark":
            # Insert into trademarks table
            columns = ", ".join(ip_asset.metadata.keys())
            placeholders = ", ".join(["%s"] * len(ip_asset.metadata))
            values = list(ip_asset.metadata.values())
            
            cursor.execute(
                f"INSERT INTO trademarks (id, {columns}) VALUES (%s, {placeholders}) ON CONFLICT (id) DO UPDATE SET {', '.join([f'{k} = EXCLUDED.{k}' for k in ip_asset.metadata.keys()])}",
                [ip_asset.id] + values
            )
    
    # Close connection
    cursor.close()
    conn.close()
    
    # Prepare IP assets for upsertion into Qdrant
    ip_assets_data = []
    for ip_asset in request.ip_assets:
        ip_assets_data.append({
            "id": ip_asset.id,
            "ip_type": ip_asset.ip_type,
            "vectors": ip_asset.vectors
        })
    
    # Upsert IP assets into IP_Assets collection
    upsert_ip_assets(ip_assets_data)
    
    # Prepare batch queries for Product_Images collection
    query_requests = []
    for ip_asset in request.ip_assets:
        if ip_asset.ip_type == "Copyright":
            # Query using copyright_clip against image_clip
            if "copyright_clip" in ip_asset.vectors:
                query_requests.append(models.QueryRequest(
                    query_vector=("image_clip", ip_asset.vectors["copyright_clip"]),
                    search_params=models.SearchParams(hnsw_ef=128, exact=False),
                    limit=20,
                    with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id"]),
                    with_vectors=False
                ))
            
            # Query using copyright_efficientnet against image_efficientnet
            if "copyright_efficientnet" in ip_asset.vectors:
                query_requests.append(models.QueryRequest(
                    query_vector=("image_efficientnet", ip_asset.vectors["copyright_efficientnet"]),
                    search_params=models.SearchParams(hnsw_ef=128, exact=False),
                    limit=20,
                    with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id"]),
                    with_vectors=False
                ))
        elif ip_asset.ip_type == "Patent":
            # Query using patent_clip_image against image_clip
            if "patent_clip_image" in ip_asset.vectors:
                query_requests.append(models.QueryRequest(
                    query_vector=("image_clip", ip_asset.vectors["patent_clip_image"]),
                    search_params=models.SearchParams(hnsw_ef=128, exact=False),
                    limit=20,
                    with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id"]),
                    with_vectors=False
                ))
            
            # Query using patent_clip_text against image_clip
            if "patent_clip_text" in ip_asset.vectors:
                query_requests.append(models.QueryRequest(
                    query_vector=("image_clip", ip_asset.vectors["patent_clip_text"]),
                    search_params=models.SearchParams(hnsw_ef=128, exact=False),
                    limit=20,
                    with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id"]),
                    with_vectors=False
                ))
        elif ip_asset.ip_type == "Trademark":
            # Query using trademark_efficientnet against image_efficientnet
            if "trademark_efficientnet" in ip_asset.vectors:
                query_requests.append(models.QueryRequest(
                    query_vector=("image_efficientnet", ip_asset.vectors["trademark_efficientnet"]),
                    search_params=models.SearchParams(hnsw_ef=128, exact=False),
                    limit=20,
                    with_payload=models.PayloadSelectorInclude(include=["client_id", "check_id"]),
                    with_vectors=False
                ))
    
    # Execute batch query
    batch_results = query_batch_points("Product_Images", query_requests)
    
    # Process results and apply filtering logic
    results = []
    ip_asset_index = 0
    query_index = 0
    
    for ip_asset in request.ip_assets:
        # Collect potential infringements for this IP asset
        potential_infringements = []
        
        # Number of queries for this IP asset
        num_queries = 0
        if ip_asset.ip_type == "Copyright":
            num_queries = sum([1 for v in ["copyright_clip", "copyright_efficientnet"] if v in ip_asset.vectors])
        elif ip_asset.ip_type == "Patent":
            num_queries = sum([1 for v in ["patent_clip_image", "patent_clip_text"] if v in ip_asset.vectors])
        elif ip_asset.ip_type == "Trademark":
            num_queries = sum([1 for v in ["trademark_efficientnet"] if v in ip_asset.vectors])
        
        # Process results for this IP asset
        for i in range(num_queries):
            for scored_point in batch_results[query_index]:
                potential_infringements.append({
                    "product_point_id": scored_point.id,
                    "client_id": scored_point.payload["client_id"],
                    "check_id": scored_point.payload["check_id"],
                    "score": scored_point.score
                })
            query_index += 1
        
        # Apply similarity filtering logic
        filtered_infringements = apply_similarity_filtering(potential_infringements, ip_asset.ip_type.lower())
        
        # Sort by score in descending order
        filtered_infringements.sort(key=lambda x: x["score"], reverse=True)
        
        # Convert to Pydantic models
        infringements = [
            ProductPointInfringement(
                product_point_id=inf["product_point_id"],
                client_id=inf["client_id"],
                check_id=inf["check_id"],
                score=inf["score"]
            )
            for inf in filtered_infringements
        ]
        
        # Add to results if there are any infringements
        if infringements:
            results.append(IPAssetInfringement(
                input_ip_asset_id=ip_asset.id,
                potential_infringements=infringements
            ))
        
        ip_asset_index += 1
    
    return ReverseCheckResponse(results=results)
