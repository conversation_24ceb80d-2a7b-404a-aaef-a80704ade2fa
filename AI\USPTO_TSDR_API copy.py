import os,re,time,asyncio,datetime,random,aiohttp,multiprocessing
start_time = time.time()
from bs4 import BeautifulSoup
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                TSDR API BeautifulSoup after  {time.time()-start_time:.2f} seconds")

import numpy as np
from fuzzywuzzy import fuzz
from logdata import log_message # Added logdata import
import json


### What works and what does not:
# When casesstatus does not work, with reg no, zip bundle might still work
# Even if status not available, image is available using the serial number
# When status is available but image is not online, it is inside (1) the status content zip and (2) the document zip in the drawing folder


### Toyota Reg no 843138 (IN_DC_1_24-cv-08880_2024-09-25_1_ 0_Exhibit_1_page26_0): the registration certificate should serial: 273449 (which does not work) but the XML show 72273449
### seria sn00381518 not found (olympic case)


# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

REG_LENGTH = 7
SER_LENGTH = 8
USPTO_API_KEYS = [os.getenv("USPTO_API_KEY"), os.getenv("USPTO_API_KEY_2")]
if not all(USPTO_API_KEYS):
    log_message("API keys not found in environment variables USPTO_API_KEY and USPTO_API_KEY_2. API calls might be limited.", level='WARNING')

ser_no_prefix_ranges = [
    (datetime.date(1870, 10, 25), datetime.date(1905, 3, 31), "70"),
    (datetime.date(1905, 4, 1), datetime.date(1955, 12, 31), "71"),
    (datetime.date(1956, 1, 1), datetime.date(1973, 8, 31), "72"),
    (datetime.date(1973, 9, 1), datetime.date(1989, 11, 15), "73"),
    (datetime.date(1989, 11, 16), datetime.date(1995, 9, 30), "74"),
    (datetime.date(1995, 10, 1), datetime.date(2000, 3, 20), "75"),
    (datetime.date(2000, 3, 21), datetime.date(2023, 12, 31), "76"),  # Paper filings
    (datetime.date(2006, 9, 14), datetime.date(2010, 3, 26), "77"),  # Internet filings
    (datetime.date(2000, 3, 21), datetime.date(2006, 9, 13), "78"),  # Internet filings
    (datetime.date(2003, 11, 3), datetime.date(2023, 12, 31), "79"),  # §66(a) filings
    (datetime.date(2010, 3, 27), datetime.date(2013, 7, 1), "85"),   # Internet filings
    (datetime.date(2013, 7, 2), datetime.date(2016, 4, 13), "86"),   # Internet filings
    (datetime.date(2016, 4, 14), datetime.date(2018, 6, 14), "87"),  # Internet filings
    (datetime.date(2018, 6, 15), datetime.date(2020, 6, 13), "88"),  # Internet filings
    (datetime.date(2020, 6, 14), datetime.date(2021, 8, 28), "90"),  # Internet filings
    (datetime.date(2021, 8, 29), datetime.date(2023, 5, 17), "97"),  # Internet filings
    (datetime.date(2023, 5, 18), datetime.date.today(), "98") # Internet filings
]

class TSDRApi:
    """
    A class to interact with the USPTO TSDR API for trademark data retrieval.
    """
    def __init__(self, api_keys=None):
        """
        Initializes the TSDRApi client.

        Args:
            api_keys (list, optional): A list of USPTO API keys. Defaults to using environment variables.
        """
        if api_keys is None:
            self.api_keys = USPTO_API_KEYS
        else:
            self.api_keys = api_keys
        self.session = None # Initialize session in start method

    async def start_session(self):
        """Starts an aiohttp client session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        """Closes the aiohttp client session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def _request_with_retry(self, url, max_retries_api=None):
        """
        Handles API requests with retry logic and rate limiting.

        Args:
            url (str): The API endpoint URL.
            headers (dict, optional): HTTP headers. Defaults to None.
            max_retries_api (int, optional): Maximum retry attempts for API requests. Defaults to API key based retries.

        Raises:
            RuntimeError: If the API request fails after maximum retries.
        """
        if max_retries_api is None:
            max_retries_api = len(self.api_keys) * 2

        current_api_index = random.randint(0, len(self.api_keys) - 1) if self.api_keys and all(self.api_keys) else 0
        retry_attempts = 0
        start_time = time.time()

        while True:
            try:
                req_headers = {}
                if self.api_keys and all(self.api_keys):
                    req_headers["USPTO-API-KEY"] = self.api_keys[current_api_index]

                log_message(f"Requesting URL: {url} with API Key index: {current_api_index if self.api_keys and all(self.api_keys) else 'N/A'}, Attempt: {retry_attempts+1}", level='DEBUG')
                async with self.session.get(url, headers=req_headers, ssl=True) as response:
                    if response.status == 200:
                        content = await response.read()
                        log_message(f"✅ API KEY {current_api_index if self.api_keys and all(self.api_keys) else 'N/A'}: URL request successful after {retry_attempts+1} attempts for URL: {url}", level='INFO')
                        return content
                    elif response.status == 404:
                        log_message(f"⚠️ API returned 404 for URL: {url}", level='WARNING')
                        return None # Handle 404 as None, caller decides how to interpret
                    elif response.status == 500:
                        # Check for "A virus was detected" message
                        try:
                            error_content = await response.json()
                            if isinstance(error_content, dict) and error_content.get("message") == "A virus was detected":
                                log_message(f"⛔ API returned 500 with 'A virus was detected' for URL: {url}. Returning None immediately.", level='ERROR')
                                return None
                        except json.JSONDecodeError:
                            # Not a JSON response, or malformed JSON, proceed with normal error handling
                            pass
                        log_message(f"⚠️ API returned status code: {response.status} for URL: {url}", level='WARNING')
                        response.raise_for_status() # Raise HTTPError for other bad responses
                    else:
                        log_message(f"⚠️ API returned status code: {response.status} for URL: {url}", level='WARNING')
                        response.raise_for_status() # Raise HTTPError for other bad responses

            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError) as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    if retry_attempts >= max_retries_api:
                        log_message(f"⚠️⚠️⚠️ Rate limit exceeded on API KEY {current_api_index} after {retry_attempts} attempts. Sleeping 60 blocking seconds. Last error: {e}", level='ERROR')
                        time.sleep(60*(retry_attempts-max_retries_api+1))
                    else:
                        if self.api_keys and all(self.api_keys):
                            log_message(f"\033[91mRate limit exceeded on API KEY {current_api_index}, trying with API KEY {(current_api_index + 1) % len(self.api_keys)}\033[0m", level='WARNING')
                            if retry_attempts >= len(self.api_keys):
                                log_message(f"\033[91m ⚠️ Rate limit exceeded for all API KEYS, waiting 60 seconds\033[0m", level='WARNING')
                                elapsed_time = time.time() - start_time
                                wait_time = max(0, 60 - elapsed_time)
                                await asyncio.sleep(wait_time)
                                start_time = time.time()

                            current_api_index = (current_api_index + 1) % len(self.api_keys)
                        else: # No API key, just wait if rate limited
                            log_message(f"\033[91mRate limit exceeded (no API key provided), waiting 60 seconds\033[0m", level='WARNING')
                            await asyncio.sleep(60)
                    retry_attempts += 1
                    log_message(f"Retrying request to URL: {url}, Attempt: {retry_attempts+1}", level='DEBUG')

                else:
                    if retry_attempts >= max_retries_api:
                        log_message(f"⚠️⚠️⚠️ API URL request failed after {retry_attempts} attempts for URL: {url}. GAME OVER Giving up. Last error: {e}", level='ERROR')
                        return
                    log_message(f"⚠️ API URL request failed. Network error => Retrying.... Attempt: {retry_attempts+1}/{max_retries_api}, Error: {e}, URL: {url}", level='WARNING')
                    retry_attempts += 1
                    await asyncio.sleep(3) # Basic backoff
                    log_message(f"Retrying request to URL: {url}, Attempt: {retry_attempts+1}", level='DEBUG')


            except Exception as e:
                if retry_attempts >= max_retries_api:
                    log_message(f"⚠️⚠️⚠️ API URL request failed after {retry_attempts} attempts for URL: {url}. GAME OVER Giving up. Last error: {e}", level='ERROR')
                    return
                log_message(f"⚠️⚠️⚠️ API URL request failed with an unexpected error for URL: {url}. Error: {e}", level='ERROR')
                retry_attempts += 1


### Single Case: pdf, zip, html, xml ###
    async def get_status_download(self, id_number, id_key='rn', format='pdf'):
        """
        Get status information as a PDF document for a given registration or serial number.
        Value: 3/10. This is short, and the info (I would expect) is also in the xml. However, the pdf has the MarkImage as an image.
        format: pdf, zip
        """
        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{id_key}{id_number}/download.{format}"
        return await self._request_with_retry(api_url)


    async def get_status_content(self, id_number, id_key='rn', format='pdf'):
        """
        Get status information as a ZIP archive for a given registration or serial number.
        Value: 6/10. This has the xml and the markImage. However, the XML is different than /info.xml, e.g. it does not include "associated marks"
        format: pdf, zip, html
        """
        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{id_key}{id_number}/content.{format}"
        return await self._request_with_retry(api_url)


    async def get_status_info_xml(self, id_number, id_key='rn'):
        """
        Get status information as XML content for a given registration or serial number.
        Value: 10/10. Is this the same as just /info?

        Returns: BeautifulSoup: Parsed XML content, or None if not found.
        """

        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{id_key}{id_number}/info"
        return await self._request_with_retry(api_url)


### Multi case status JSON ###
    async def get_status_info_json_multi(self, id_numbers, id_key='rn'):
        """
        Get status information as JSON content for a list of registration or serial numbers.
        Value: 0/10. No trademark, no certificate.
        """
        formatted_ids = ",".join(id_numbers)
        api_url = f"https://tsdrapi.uspto.gov/ts/cd/caseMultiStatus/{id_key}?ids={formatted_ids}"
        return await self._request_with_retry(api_url)

### Bundles ###
    async def get_casedocs_bundle(self, id_numbers, id_key='rn', option=None, format='pdf'):
        """
        Get all case documents as a PDF bundle for a SINGLE case, for a given registration or serial number.
        Does it work for multiple cases?
        Value: 8/10, in the only example I checked, it had all 360 pages of General Motors trademarks
        doc_type: SPE (specimen == product images examples), RC (registration certificate), etc.
        Without a doc_type it returns all the documents, incl application, registration certificate, specimen images, etc. => no value
        format: pdf, zip, xml
        """

        if option and option == 'category=RC' and id_key != 'rn': # Always 'rn' for registration numbers not for serial numbers (sn)
            log_message("get_reg_certificate_pdf function is only applicable for registration numbers ('reg').", level='WARNING')
            return None

        formatted_ids = ",".join(id_numbers)

        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casedocs/bundle.{format}?{id_key}={formatted_ids}"
        if option:
            api_url += f"&{option}"
        return await self._request_with_retry(api_url)


### Process results ###
    def process_xml_content(self, content):
        """
        Process the XML content and return a dictionary with the processed data.
        """
        # Parse response
        data = {}
        soup = BeautifulSoup(content, "xml")

        data["reg_no"] =  (soup.find("ns1:RegistrationNumber").text) if soup.find("ns1:RegistrationNumber") else None  # # Trademark applied for but not granted yet: should never happen because when we search for trademarks we filter out pending trademarks
        data["text"] =  (soup.find("ns2:MarkReproduction").find("ns2:MarkVerbalElementText").text)
        data["date"] =  (soup.find("ns1:RegistrationDate").text) if soup.find("ns1:RegistrationDate") else None
        country_codes = [country_code.text for country_code in soup.find_all("ns1:IncorporationCountryCode")]
        data["country_codes"] = list(set(country_codes))
        data["nb_suits"] = sum([1 for event in soup.find_all("ns2:MarkEventCode") if event.text == "NOSUI"])
        data["application_number"] = soup.find("ns1:ApplicationNumberText").text if soup.find("ns1:ApplicationNumberText") else None
        data["applicant_name"] = soup.find("ns1:OrganizationStandardName").text if soup.find("ns1:OrganizationStandardName") else None

        data["associated_marks"] = []
        for mark in soup.find_all("ns2:AssociatedMark"):
            formatted_string = ""
            for child in mark.children:
                if child.name is not None:  # Check if it's a tag
                    formatted_string += f"{child.text} :"
            data["associated_marks"].append(formatted_string.strip(":").strip())

        img_location = soup.find("ns2:MarkImageBag").find("ns1:FileName").text
        pure_img_location = re.sub("[^0-9]", "", img_location)
        if pure_img_location != "":
            data["pure_img_location"] = pure_img_location
            data["image_url"] = f"https://tsdr.uspto.gov/img/{pure_img_location}/large"

        classification_obj = [cl_set.find("ns2:ClassNumber") for cl_set in soup.find_all("ns2:GoodsServicesClassification")]
        int_cls = set()
        for cl_set in classification_obj:
            if cl_set:
                try:
                    int_cls.add(int(cl_set.text))
                except:
                    log_message(f"🔥 Error: Found '{cl_set.text}' as a classification number", level='ERROR')
        data["int_cls"] = sorted(list(int_cls))

        return data


    async def download_from_uspto(self, url):
        # Download an image from the USPTO website

        request_headers = {"User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"),
            "Accept": "image/webp,image/apng,image/*,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Connection": "keep-alive"}

        max_retries = 5  # Increased from 3 to 5
        timeout = aiohttp.ClientTimeout(total=60)  # Increased timeout to 60 seconds

        for attempt in range(max_retries):
            try:
                # Ensure session is active
                if self.session is None or self.session.closed:
                    await self.close_session()  # Ensure clean closure
                    await self.start_session()

                async with self.session.get(url, headers=request_headers, timeout=timeout) as response:
                    if response.status != 200:
                        response.raise_for_status()
                    return await response.read()

            except asyncio.TimeoutError:
                log_message(f"⚠️ Timeout occurred on attempt {attempt+1}/{max_retries} for URL {url}", level='WARNING')
                if attempt == max_retries - 1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction timed out after {max_retries} attempts")
                await asyncio.sleep(2 ** attempt + random.uniform(1, 3))

            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError, RuntimeError) as e:
                log_message(f"⚠️⚠️⚠️ USPTO image extraction failed after {attempt+1}/{max_retries} attempts for URL {url}. Error: {e}", level='ERROR')

                # Close and recreate session on certain errors
                if any(err in str(e).lower() for err in ["file descriptor", "operation now in progress", "connection reset"]):
                    await self.close_session()
                    await asyncio.sleep(1)  # Brief pause before creating new session
                    await self.start_session()

                if attempt == max_retries - 1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction failed after {max_retries} attempts: {e}")

                # Exponential backoff with jitter
                wait_time = (2 ** attempt) + random.uniform(1, 3)
                await asyncio.sleep(wait_time)

            except Exception as e:
                log_message(f"⚠️⚠️⚠️ Unexpected error during USPTO image extraction for URL {url}: {e}", level='ERROR')
                if attempt == max_retries - 1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction failed with unexpected error: {e}")
                await asyncio.sleep(2 ** attempt + random.uniform(1, 3))
                
                
    async def format_ser_number(self, ser_number, case_date=None, plaintiff_names=None):
        """
        Remove non-numeric characters from the id number and pad with zeros to standard length.
        """
        if ser_number is None:
            return None
        
        if isinstance(plaintiff_names, str):
            plaintiff_names = json.loads(plaintiff_names)
            
        pure_ser_no = re.sub("[^0-9]", "", ser_number)
        pure_reg_no = None
        # Serial numbers are 8 digits: 2-digit series code + 6-digit number
        if len(pure_ser_no) != SER_LENGTH:
            # First try to find the correct serial number by plaintiff match if we have plaintiff info
            log_message(f"Serial number '{pure_ser_no}' requires plaintiff match validation to determine the 1st 2 digits")
            if plaintiff_names and case_date:        
                historical_series_codes = list(set([code for start_date, end_date, code in ser_no_prefix_ranges if end_date <= case_date])) # A series is relevant if its end_date is on or before date_to_check_against
                
                if not historical_series_codes:
                    log_message(f"No historical series codes found for date from {case_date}.")
                    return None

                # Ensure base_serial_digits is the core 6 digits
                numeric_part_of_base_ser = re.sub("[^0-9]", "", pure_ser_no)
                core_serial_digits = numeric_part_of_base_ser[-6:] if len(numeric_part_of_base_ser) >= 6 else numeric_part_of_base_ser.zfill(6)
                
                async def get_names_from_xml(candidate_sn):
                    xml_content = await self.get_status_info_xml(candidate_sn, id_key='sn')
                    if xml_content:
                        processed_data = self.process_xml_content(xml_content)
                        xml_extracted_plaintiff_name = processed_data.get("xml_plaintiff_name")
                        applicant_name = processed_data.get("applicant_name")
                        candidate_rn = processed_data.get("reg_no")
                        return candidate_sn, candidate_rn, xml_extracted_plaintiff_name, applicant_name
                    else:
                        return None, None, None, None
                        
                get_names_from_xml_tasks = []
                for series_code in historical_series_codes:
                    candidate_sn = f"{series_code}{core_serial_digits}"
                    get_names_from_xml_tasks.append(get_names_from_xml(candidate_sn))
                    
                results = await asyncio.gather(*get_names_from_xml_tasks)
                
                best_match_score = 0
                best_plaintiff_name = None
                best_applicant_name = None
                for candidate_sn, candidate_rn, xml_extracted_plaintiff_name, applicant_name in results:
                    if xml_extracted_plaintiff_name:
                        for plaintiff_name in plaintiff_names:
                            match_score = fuzz.partial_ratio(plaintiff_name.lower(), xml_extracted_plaintiff_name.lower())
                            if match_score > best_match_score:
                                best_match_score = match_score
                                pure_ser_no = candidate_sn
                                pure_reg_no = candidate_rn
                                best_plaintiff_name = plaintiff_name
                                best_applicant_name = xml_extracted_plaintiff_name
                    if applicant_name:
                        for plaintiff_name in plaintiff_names:
                            match_score = fuzz.partial_ratio(plaintiff_name.lower(), applicant_name.lower())
                            if match_score > best_match_score:
                                best_match_score = match_score
                                pure_ser_no = candidate_sn
                                pure_reg_no = candidate_rn
                                best_plaintiff_name = plaintiff_name
                                best_applicant_name = applicant_name
                
                print(f"format_ser_number: Found the best candidate: {pure_ser_no}, based on a match score of {best_match_score} between the plaintiff name {best_plaintiff_name} and the name in the XML {best_applicant_name}.")
        
                if not pure_reg_no:
                    print("\033[91m !!! ❌ Did not find a valid trademark registration number for the best serial number match! \033[0m ")
                else:
                    pure_reg_no = pure_reg_no.zfill(REG_LENGTH)
                

                
        if len(pure_ser_no) != SER_LENGTH:
            print("\033[91m !!! ❌ Did not find a valid trademark serial number by trying all combinations! \033[0m ")
            
        return pure_ser_no, pure_reg_no


def format_reg_number(id_number):
    """
    Remove non-numeric characters from the id number and pad with zeros to standard length.
    """
    if id_number is None:
        return None
    
    pure_id_no = re.sub("[^0-9]", "", str(id_number))
    pure_id_no = pure_id_no.zfill(REG_LENGTH)
    return pure_id_no 

async def test_api_calls():
    api_client = TSDRApi()
    await api_client.start_session()

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    base_folder = f"test_results_{timestamp}"
    os.makedirs(base_folder, exist_ok=True)

    test_reg_numbers = ["6,181,442", "1871376", "5004543"]
    test_reg_numbers = [1346436,5546145,6731985,6731984,2854465,7426341,5276717,6731983,6069963,1100741,1075059,5697522,2772224,1314299,1312269,1390935,2276097,7139772,6294707,5428677]

    # Problem reg numbers from case 61411
    # test_reg_numbers =  ['626,035', '902,190', '1,177,400', '1,241,264', '1,241,265', '1,314,511', '1,347,677', '1,501,898', '1,733,051', '1,734,822', '2,559,772', '2,964,843', '3,025,936', '3,133,139', '3,134,695', '3,149,203', '3890159', '4074269', '4241822', '5100448', '5166441', '5280486', '5912273'] # from case 61411
    # test_reg_numbers = ["3306673", ]
    formatted_reg_ids = [format_reg_number(reg_no) for reg_no in test_reg_numbers]
    id_key = 'rn'
    
    # test_ser_numbers = ["85056387", "78758300", "78895949"] # Using dummy serial numbers, replace with real ones if needed
    # formatted_reg_ids = [await api_client.format_ser_number(ser_no, case_date=None, plaintiff_names=None) for ser_no in test_ser_numbers]
    # id_key = 'sn'
    
    # Test serial number that are too short
    # test_ser_numbers = ["65730"] # Using dummy serial numbers, replace with real ones if needed
    # formatted_reg_ids = [await api_client.format_ser_number(ser_no, case_date=datetime.date(2025, 5, 5), plaintiff_names=["Universal City Studios"]) for ser_no in test_ser_numbers]
    # id_key = 'sn'


    async def save_content(content, filename, method_folder):
        if content:
            method_path = os.path.join(base_folder, method_folder)
            os.makedirs(method_path, exist_ok=True)
            filepath = os.path.join(method_path, filename)
            try:
                with open(filepath, 'wb') as f:
                    f.write(content)
                log_message(f"✅ Saved {filename} to {filepath}", level='INFO')
                return True
            except Exception as e:
                log_message(f"🔥 Error saving {filename}: {e}", level='ERROR')
                return False
        else:
            log_message(f"⚠️ No content received for {filename}, not saving.", level='WARNING')
            return False



    log_message("🧪 Starting tests for single registration/serial numbers...", level='INFO')
    for reg_no in formatted_reg_ids: # Test with first 2 reg nos
        # sn_no = test_ser_numbers[test_reg_numbers.index(reg_no)] # Corresponding SN for testing
        # method_folder = f"single_reg_{reg_no}_sn_{sn_no}"
        method_folder = f"single_reg_{reg_no}"

        # This XML has way more info that than one in the Content or Download.
        # xml_content_reg = await api_client.get_status_info_xml(reg_no, id_key=id_key)
        # await save_content(xml_content_reg, f"status_info_reg_{reg_no}.xml", method_folder)

        # Download Zip and Content Zip = markImage.jpg + xml + html. But "Content" seems to have more info than "Download".
        # Download PDF and Content PDF = 1 pdf document that is the same as the xml / html in the zip. "Content" is 10 pages with associated marks, while "Download" is 3 page.
        # download_reg = await api_client.get_status_download(reg_no, id_key=id_key, format='zip')
        # await save_content(download_reg, f"status_download_reg_{reg_no}.zip", method_folder)
        # content_reg = await api_client.get_status_content(reg_no, id_key=id_key, format='zip')
        # await save_content(content_reg, f"status_content_reg_{reg_no}.zip", method_folder)


        # Get a single registration certificate as PDF
        # reg_cert_pdf_content = await api_client.get_casedocs_bundle([reg_no], id_key=id_key, format='zip')
        # await save_content(reg_cert_pdf_content, f"reg_cert_{reg_no}.zip", method_folder)
        # reg_cert_pdf_content = await api_client.get_casedocs_bundle([reg_no], id_key=id_key, format='pdf', option='category=RC')
        # await save_content(reg_cert_pdf_content, f"reg_cert_{reg_no}.pdf", method_folder)


    log_message("🧪 Starting tests for multiple registration numbers...", level='INFO')
    multi_method_folder = "multi_cases"



    # Multi JSON: does not have text, image or certificate => useless
    # multi_json_status = await api_client.get_status_info_json_multi(formatted_reg_ids[:2], id_type='reg')
    # await save_content(multi_json_status, f"multi_reg1.json", multi_method_folder)

    # Bundle
    # This return all the documents into a bundle for multiple cases (or a single case), either PDF or ZIP
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='pdf')
    # await save_content(reg_cert_pdf_content, f"multi_reg.pdf", multi_method_folder)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='zip')
    # await save_content(reg_cert_pdf_content, f"multi_reg.zip", multi_method_folder)

    # This is great, return all the certification for multiple cases. If in Zip format => the different certificates are in different format: PDF, TIFF, etc.
    print(f"Total number of reg number {len(formatted_reg_ids)}")
    reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:18], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    time.sleep(35)
    reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:20], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    # time.sleep(35)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:22], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    # time.sleep(35)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:24], id_key=id_key, format='zip', option='category=RC') # Test with first 2 ser nos
    # time.sleep(35)

    # await save_content(reg_cert_pdf_content, f"multi_reg_reg_cert.pdf", multi_method_folder)
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='zip', option='category=RC') # Test with first 2 ser nos
    # await save_content(reg_cert_pdf_content, f"multi_reg_reg_cert.zip", multi_method_folder)

    # This works, but useless because we do not need the specimen (product images)
    # specimen_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='pdf', option='type=SPE') # Test with first 2 ser nos
    # await save_content(specimen_pdf_content, f"multi_reg_specimen.pdf", multi_method_folder)

    # This is multiple cases in 1 xml. The content is the same as the get_casedocs_bundle(format='xml')
    # This has the link to the Registration certificate in section  <DocumentTypeCodeDescriptionText> Registration Certificate </DocumentTypeCodeDescriptionText> => pdf or tiff. If multiple, take the latest.
    # reg_cert_pdf_content = await api_client.get_casedocs_bundle(formatted_reg_ids[:2], id_type='reg', format='xml')
    # await save_content(reg_cert_pdf_content, f"multi_reg2.xml", multi_method_folder)

    await api_client.close_session()
    log_message(f"✅ All tests completed. Results saved in '{base_folder}'", level='INFO')


if __name__ == "__main__":
    asyncio.run(test_api_calls())