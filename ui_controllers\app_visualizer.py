import json, os, threading, queue, time, asyncio
import pandas as pd
import numpy as np
from datetime import datetime
from flask import jsonify, request, abort, Response
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch, save_df_to_feather
from Alerts.PicturesProcessing.ProcessPictures import get_template # Keep this import if needed
import cache_manager
from logdata import log_message # Import log_message
from Fixes.Plaintiff_Clean_Up import improve_single_plaintiff_name as process_plaintiff_improvement
from Common.Constants import nas_case_folder, local_case_folder, sanitize_name
from AI.Translation import translate_steps_for_a_case # Keep if AI tasks are triggered separately or for other functions
# Import the new WorkflowManager
from Alerts import WorkflowManager
from FileManagement.NAS import NASConnection # Keep if used elsewhere or directly
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_id # Keep for DB ops
# Removed: from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import get_table_from_GZ # Added

# --- Background Task Management ---
# Define queue-related variables here as they are used by routes in this file
update_progress_queues = {}
case_update_queue = []
case_processing_active = False
# Keep copyright template logic here if process_case_update uses it.
copyright_template1_hash = copyright_template1_size = None

# Import background task processing functions (adjust path if necessary)
# from Fixes.Plaintiff_Clean_Up import process_case_update, process_next_case_in_queue # Example if moved
# Assuming these are defined later in this file for now

# --- API Routes ---

def get_cases_with_filters():
    """API endpoint to get filtered case data using cached DataFrames."""
    # Removed global declarations for cache variables as they are imported

    # Get query parameters
    case_number = request.args.get('case_number', '')
    plaintiff_name = request.args.get('plaintiff_name', '')
    plaintiff_id = request.args.get('plaintiff_id', '')
    case_types = request.args.getlist('case_type[]')  # Get as list for multiple selection
    picture_type = request.args.get('picture_type', '')
    validation_status = request.args.get('validation_status', '')
    ip_source = request.args.get('ip_source', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    sort_by = request.args.get('sort_by', 'date_filed')
    sort_order = request.args.get('sort_order', 'desc')
    limit = int(request.args.get('limit', 20))
    offset = int(request.args.get('offset', 0))
    refresh = request.args.get('refresh', 'false').lower() == 'true'
    update_time_from = request.args.get('update_time_from', '')
    update_time_to = request.args.get('update_time_to', '')

    # Load or refresh data using the imported function
    if not cache_manager.refresh_cached_data(force=refresh): # Check return value
        log_message("Failed to load data cache in get_cases_with_filters.", level="ERROR")
        return jsonify({"error": "Failed to load data cache. Please try again later."}), 500

    # Check if cache loading failed (redundant check, but safe)
    if cache_manager.cached_cases_df is None or cache_manager.cached_plaintiff_df is None:
        log_message("Data cache is unavailable in get_cases_with_filters.", level="ERROR")
        return jsonify({"error": "Data cache is unavailable."}), 500


    # Merge data using the copies
    merged_df = pd.merge(cache_manager.cached_cases_df, cache_manager.cached_plaintiff_df,
                         how='left', left_on='plaintiff_id', right_on='id',
                         suffixes=('', '_plaintiff'))

    # Apply filters
    if case_number:
        merged_df = merged_df[merged_df['docket'].str.contains(case_number, case=False, na=False)]
    if plaintiff_name:
        merged_df = merged_df[merged_df['plaintiff_name'].str.contains(plaintiff_name, case=False, na=False)]
    if plaintiff_id:
        # Convert both the DataFrame column and the input to string for exact comparison
        # This handles cases where plaintiff_id might be stored as int or string
        merged_df = merged_df[merged_df['plaintiff_id'].astype(str) == plaintiff_id]

    # Filter by validation status
    if validation_status:
        merged_df = merged_df[merged_df['validation_status'] == validation_status]

    # Filter by case types (multi-select)
    if case_types:
        case_type_filter = merged_df['nos_description'].str.contains('|'.join(case_types), case=False, na=False)
        merged_df = merged_df[case_type_filter]

    # Filter by date range
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            merged_df = merged_df[pd.to_datetime(merged_df['date_filed'], errors='coerce') >= date_from]
        except ValueError:
            pass
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            merged_df = merged_df[pd.to_datetime(merged_df['date_filed'], errors='coerce') <= date_to]
        except ValueError:
            pass

    # Filter by update time range
    if update_time_from:
        try:
            update_time_from_dt = datetime.strptime(update_time_from, '%Y-%m-%d')
            merged_df = merged_df[pd.to_datetime(merged_df['update_time'], errors='coerce') >= update_time_from_dt]
        except ValueError:
            pass # Ignore invalid date format
    if update_time_to:
        try:
            update_time_to_dt = datetime.strptime(update_time_to, '%Y-%m-%d')
            # Add one day to include the entire 'to' date
            update_time_to_dt += pd.Timedelta(days=1)
            merged_df = merged_df[pd.to_datetime(merged_df['update_time'], errors='coerce') < update_time_to_dt]
        except ValueError:
            pass # Ignore invalid date format

    # Filter by IP source - check if any IP type has count > 0 for the selected source
    if ip_source:
        def has_ip_source(row):
            if pd.isna(row.get('images_status')):
                return False

            images_status = row.get('images_status', {})
            if not isinstance(images_status, dict):
                return False

            # Check all IP types for the source
            for ip_type in ['trademark_status', 'patent_status', 'copyright_status']:
                status = images_status.get(ip_type, {})
                if status and status.get(ip_source, {}).get('count', 0) > 0:
                    return True
            return False

        merged_df = merged_df[merged_df.apply(has_ip_source, axis=1)]

    # Filter by picture type
    if picture_type:
        if picture_type == 'no_ip':
            # Filter cases with no IP information
            def has_no_ip(images_data):
                if pd.isna(images_data):
                    return True
                try:
                    # Check if all IP type dictionaries are empty
                    return (not images_data.get('trademarks', {}) and
                            not images_data.get('patents', {}) and
                            not images_data.get('copyrights', {}))
                except:
                    return False

            merged_df = merged_df[merged_df['images'].apply(has_no_ip)]
        else:
            # Filter by specific IP type
            def has_picture_type(images_data):
                if pd.isna(images_data):
                    return False
                try:
                    # Images data should already be decoded at this point
                    return bool(images_data.get(picture_type, {}))
                except:
                    return False

            merged_df = merged_df[merged_df['images'].apply(has_picture_type)]

    # Sort data
    sort_columns = {
        'date_filed': 'date_filed',
        'update_time': 'update_time', # Add update_time for sorting
        'plaintiff_name': 'plaintiff_name',
        'plaintiff_id': 'plaintiff_id',
        'docket': 'docket',
        'court': 'court',
        'pictures': 'images'  # This is approximate since we need to count pictures
    }

    sort_col = sort_columns.get(sort_by, 'date_filed')
    # Ensure the sort column exists before sorting
    if sort_col in merged_df.columns:
        # Handle potential NaT values in date columns before sorting
        if pd.api.types.is_datetime64_any_dtype(merged_df[sort_col]):
            # Fill NaT with a very old date for ascending, very new for descending
            fill_value = pd.Timestamp.min if sort_order.lower() == 'asc' else pd.Timestamp.max
            merged_df[sort_col] = merged_df[sort_col].fillna(fill_value)

        merged_df = merged_df.sort_values(by=sort_col, ascending=(sort_order.lower() == 'asc'), na_position='last')
    else:
        log_message(f"Warning: Sort column '{sort_col}' not found in DataFrame. Defaulting to 'date_filed'.", level="WARNING")
        merged_df = merged_df.sort_values(by='date_filed', ascending=(sort_order.lower() == 'asc'), na_position='last')

    # Count total results
    total_count = len(merged_df)

    # Paginate
    merged_df = merged_df.iloc[offset:offset+limit]

    # Prepare proposed plaintiff names from the reviews table
    proposed_names = {}
    if cache_manager.cached_plaintiff_reviews is not None and not cache_manager.cached_plaintiff_reviews.empty:
        # Create a dictionary of case_id -> proposed_name for cases that have a non-empty proposed name
        for _, row in cache_manager.cached_plaintiff_reviews.iterrows():
            if pd.notna(row.get('proposed_name')) and row.get('proposed_name') != '':
                proposed_names[str(row.get('case_id'))] = row.get('proposed_name')

    # Format results
    result_data = []
    for _, row in merged_df.iterrows():
        # Format court name - remove "District Court"
        court = row.get('court', '')
        if court and isinstance(court, str):
            court = court.replace(' District Court', '')

        case_data = {
            'id': row.get('id'),
            'docket': row.get('docket'),
            'court': court,
            'plaintiff_name': row.get('plaintiff_name'),
            'plaintiff_id': row.get('plaintiff_id'),
            'nos_description': row.get('nos_description'),
            'class_code': row.get('class_code'),
            'date_filed': row.get('date_filed'),
            'plaintiff_overview': row.get('plaintiff_overview'),
            'aisummary': row.get('aisummary'),
            'validation_status': row.get('validation_status', ''),
            'images_status': row.get('images_status', {}),
            'update_time': row.get('update_time') # Add update_time to case data
        }

        # Process images
        images = {'trademarks': [], 'patents': [], 'copyrights': []}
        if not pd.isna(row.get('images')):
            try:
                images_data = row.get('images')  # Already decompressed

                # Process trademarks
                for key, value in images_data.get('trademarks', {}).items():
                    if isinstance(value, dict):
                        image_data = {
                            'image': key,
                            'trademarkText': value.get('trademark_text'),
                            'regNo': value.get('reg_no'),
                            'intClsList': value.get('int_cls_list'),
                            'fullFilename': value.get('full_filename')
                        }
                        images['trademarks'].append(image_data)

                # Process patents
                for key, value in images_data.get('patents', {}).items():
                    if isinstance(value, dict):
                        patent_number = value.get('patent_number')
                        inventors = None
                        applicant = None
                        assignee = None

                        if patent_number and cache_manager.cached_tro_patents_df is not None and not cache_manager.cached_tro_patents_df.empty:
                            # Assuming 'document_id' is the column in cached_tro_patents_df corresponding to patent_number
                            # Adjust type comparison if necessary (e.g., str(patent_number))
                            patent_info = cache_manager.cached_tro_patents_df[cache_manager.cached_tro_patents_df['document_id'] == patent_number]
                            if not patent_info.empty:
                                patent_row = patent_info.iloc[0]
                                # Fetch details from the patents DataFrame, provide defaults if columns don't exist
                                inventors = patent_row.get('inventors')
                                applicant = patent_row.get('applicant')
                                assignee = patent_row.get('assignee')

                        image_data = {
                            'image': key,
                            'productName': value.get('product_name'),
                            'inventors': inventors,
                            'applicant': applicant,
                            'assignee': assignee,
                            'patentNumber': patent_number,
                            'fullFilename': value.get('full_filename')
                        }
                        images['patents'].append(image_data)

                # Process copyrights
                for key, value in images_data.get('copyrights', {}).items():
                    if isinstance(value, dict):
                        image_data = {
                            'image': key,
                            'regNo': value.get('reg_no'),
                            'fullFilename': value.get('full_filename')
                        }
                        images['copyrights'].append(image_data)
            except Exception as e:
                log_message(f"Error processing images for case {row.get('id')}: {e}", level="ERROR", exc_info=True)

            case_data.update(images)
            result_data.append(case_data)

    return jsonify({
        'total': total_count,
        'offset': offset,
        'limit': limit,
        'data': result_data,
        'last_refresh': cache_manager.last_refresh_time.isoformat() if cache_manager.last_refresh_time else None,
        'proposed_names': proposed_names  # Add proposed plaintiff names to the response
    })

def update_validation_status():
    """API endpoint to update validation status of a case"""
    # Get request data
    data = request.get_json()
    if not data or 'case_id' not in data or 'validation_status' not in data:
        return jsonify({'error': 'Missing required fields (case_id, validation_status)'}), 400

    try:
        case_id = int(data['case_id'])
        validation_status = data['validation_status']
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid case ID format or validation status'}), 400

    # Validate status values
    valid_statuses = ['validated', 'review_required', 'failed', '']
    if validation_status not in valid_statuses:
        return jsonify({'error': f'Invalid validation status. Must be one of: {valid_statuses}'}), 400

    # Ensure cases data is loaded
    if cache_manager.cached_cases_df is None:
        log_message("Cached cases data is None in update_validation_status, attempting to load.", level="WARNING")
        if not cache_manager.refresh_cached_data(force=True): # Force refresh if cache is empty
            return jsonify({'error': 'Case data not available'}), 500

    # Find the index of the case by ID
    case_indices = cache_manager.cached_cases_df.index[cache_manager.cached_cases_df['id'] == case_id].tolist()

    if not case_indices:
        log_message(f"update_validation_status: case not found for case_id: {case_id}", level="WARNING")
        return jsonify({'error': 'Case not found'}), 404

    # Assume only one case matches the ID
    idx = case_indices[0]

    # Update the validation status and update_time in the cached DataFrame
    cache_manager.cached_cases_df.loc[idx, 'validation_status'] = validation_status
    current_time = pd.Timestamp.now(tz='UTC') # Use UTC for consistency
    cache_manager.cached_cases_df.loc[idx, 'update_time'] = current_time

    try:
        # Get required fields for database update
        row_df = cache_manager.cached_cases_df.loc[[idx]]
        required_fields = ['id', 'plaintiff_id', 'docket', 'court', 'date_filed', 'title', 'validation_status'] # These field cannot be null in database. Upsert first tries insert then it fail then update. So these field must be there to try the insert.
        update_df = row_df[required_fields]
        
        # Update database
        insert_and_update_df_to_GZ_batch(update_df, "tb_case", "id")
        log_message(f"update_validation_status: updated case {case_id} to '{validation_status}' in database.", level="INFO")
        
        # Save to feather file, non blocking (for speed)
        # OLD: save_df_to_feather(cache_manager.cached_cases_df, "tb_case")
        def save_feather_background():
            save_df_to_feather(cache_manager.cached_cases_df, "tb_case")
        
        # Start a background thread to save the feather file
        thread = threading.Thread(target=save_feather_background)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'case_id': case_id,
            'validation_status': validation_status,
            'update_time': current_time.strftime('%Y-%m-%d')
        })

        

        
    except Exception as e:
        log_message(f"Error updating validation status for case {case_id}: {e}", level="ERROR")
        return jsonify({
            'success': False,
            'error': f'Failed to update validation status: {str(e)}'
        }), 500


# --- Background Task Handling Routes ---

def update_existing_case():
    """API endpoint to queue an update for an existing case."""
    # Uses global queue variables defined at the top of this file
    global update_progress_queues, case_update_queue, case_processing_active

    data = request.get_json()
    if not data or 'case_id' not in data:
        return jsonify({'error': 'Missing required fields (case_id)'}), 400

    try:
        case_id = int(data['case_id'])
    except ValueError:
        return jsonify({'error': 'Invalid case ID format'}), 400

    # file_type determines if LexisNexis is used and which files to get. Empty means use DocketBird.
    file_type = data.get('file_type', '')
    # update_steps determines if *any* step update logic (Lexis or DocketBird) should run.
    update_steps = data.get('update_steps', True) # Default to True if not provided
    force_docket_refresh = data.get('force_docket_refresh', False)

    # Create a queue for this case if it doesn't exist
    if case_id not in update_progress_queues:
        update_progress_queues[case_id] = queue.Queue()

    # Check if this case is already in the queue
    for queued_case in case_update_queue:
        if queued_case['case_id'] == case_id:
            return jsonify({'success': True, 'message': 'Case already queued for update', 'case_id': case_id, 'queued': True})

    # Add case to the queue
    case_update_queue.append({
        'case_id': case_id,
        'file_type': file_type,
        'force_docket_refresh': force_docket_refresh,
        'update_steps': update_steps # Add update_steps to the queue item
    })

    q = update_progress_queues[case_id]
    q.put(f"Case added to queue. Position: {len(case_update_queue)}")

    # If no case is being processed, start processing the queue
    if not case_processing_active:
        process_next_case_in_queue()
    else:
        q.put(f"Waiting for other cases to finish processing. Current queue position: {len(case_update_queue)}")

    return jsonify({'success': True, 'message': 'Update queued', 'case_id': case_id, 'queue_position': len(case_update_queue)})

def stream_update_status(case_id):
    """Stream update status for a specific case"""
    case_id = int(case_id)

    def generate():
        if case_id not in update_progress_queues:
            yield 'data: No update in progress for this case\n\n'
            return

        q = update_progress_queues[case_id]

        # Initial message
        yield 'data: Connecting to update stream...\n\n'

        # For dot progression
        empty_counter = 0

        # Keep checking the queue for new messages
        while True:
            try:
                # Non-blocking get with timeout
                message = q.get(block=True, timeout=1.0)

                if message == "DONE":
                    # End of updates
                    yield 'data: Update completed\n\n'
                    break

                # Reset dot counter when new message arrives
                empty_counter = 0
                yield f'data: {message}\n\n'

            except queue.Empty:
                # Add a dot every 5 seconds instead of a new line
                empty_counter += 1
                if empty_counter == 5:
                    yield 'data: .'
                    empty_counter = 0

            except Exception as e:
                yield f'data: Error: {str(e)}\n\n'
                break

    return Response(generate(), mimetype='text/event-stream')

def process_next_case_in_queue():
    """Process the next case in the queue"""
    global case_update_queue, case_processing_active

    if not case_update_queue:
        case_processing_active = False
        return

    case_processing_active = True
    next_case = case_update_queue[0]

    # Start update in a background thread
    # Retrieve update_steps from the queued item
    update_steps = next_case.get('update_steps', True) # Default to True if missing

    thread = threading.Thread(
        target=process_case_update,
        args=(next_case['case_id'], next_case['file_type'], next_case['force_docket_refresh'], update_steps) # Pass update_steps
    )
    thread.daemon = True
    thread.start()

def process_case_update(case_id, file_type, force_docket_refresh, update_steps):
    """Process case update in background thread using WorkflowManager"""
    global case_update_queue, case_processing_active # Keep global refs for queue management

    q = update_progress_queues.get(case_id)
    if not q:
        log_message(f"Error: Progress queue not found for case {case_id} in process_case_update.", level="ERROR")
        # Attempt to clean up and move to next, though this state shouldn't happen
        if case_update_queue and case_update_queue[0]['case_id'] == case_id:
             case_update_queue.pop(0)
        process_next_case_in_queue()
        return

    # Log the queue status
    queue_position = 1
    current_queue_len = len(case_update_queue)
    for i, queued_case in enumerate(case_update_queue):
        if queued_case['case_id'] == case_id:
            queue_position = i + 1
            break
    # Send progress as dictionary for structured updates
    q.put({"message": f"Processing case {case_id}. Queue position: {queue_position}/{current_queue_len}", "percentage": 0})

    start_time = time.time()
    log_message(f"Starting update for case {case_id} via WorkflowManager. file_type: '{file_type}', force_refresh: {force_docket_refresh}, update_steps: {update_steps}", level="INFO")

    try:
        # --- Prepare options for WorkflowManager.process_single_case ---
        # Determine file strategy based on file_type parameter
        if file_type == 'ip_only':
            file_strategy = "FILE_TYPE_STRATEGY_IP_ONLY"
            source_msg = "LexisNexis (IP Files Only)"
        elif file_type == 'all':
            file_strategy = "FILE_TYPE_STRATEGY_ALL"
            source_msg = "LexisNexis (All Files)"
        elif file_type == 'none':
            file_strategy = "FILE_TYPE_STRATEGY_NONE"
            source_msg = "LexisNexis (Docket Only)"
        else: # Empty or unrecognized file_type might indicate a different source or default
              # For now, assume default is IP_ONLY if using WorkflowManager, or adjust as needed.
              # If DocketBird was the only alternative, this refactoring assumes its logic is either
              # integrated into CaseProcessor or deprecated for visualizer updates.
            file_strategy = "FILE_TYPE_STRATEGY_IP_ONLY" # Defaulting to IP_ONLY if file_type is empty/invalid
            source_msg = f"LexisNexis (IP Files Only - Default for file_type='{file_type}')"
            log_message(f"Unrecognized or empty file_type '{file_type}' for case {case_id}. Defaulting to IP_ONLY strategy.", level="WARNING")

        q.put({"message": f"Using {source_msg}", "percentage": 5})

        # Define the options dictionary
        processing_options = {
            'file_type_strategy': file_strategy,
            'force_refresh': force_docket_refresh,
            'update_steps': update_steps,
            'process_pictures': True, # Keep processing pictures for updates
            'upload_files': True,   # Keep uploading files
            # 'run_ai_tasks': True,   # Deprecated - use specific flags
            'run_plaintiff_overview': True, # Enable AI for visualizer updates
            'run_summary_translation': True,
            'run_step_translation': True,
            'refresh_cache_after_ai': True, # Refresh cache after AI runs
            'max_retries': 3,       # Example default
            'delay_between_retries': 5 # Example default
            # Add any other options relevant for single case updates
        }

        # --- Call WorkflowManager ---
        # process_single_case handles logging, DB updates, and uses CaseProcessor internally
        WorkflowManager.process_single_case(
            case_id=case_id,
            options=processing_options,
            progress_queue=q # Pass the queue for progress updates
        )
        # Note: process_single_case should ideally log its own success/failure and report final progress via the queue.
        # We rely on the "DONE" message put by CaseProcessor's report_progress or process_single_case's finally block.

        duration = time.time() - start_time
        # Final message might be redundant if process_single_case sends 100% completion message
        # q.put({"message": f"Update process finished in {duration:.2f} seconds.", "percentage": 100})
        log_message(f"WorkflowManager process for case {case_id} finished in {duration:.2f} seconds.", level="INFO")

    except Exception as e:
        duration = time.time() - start_time
        error_message = f"Critical error during update for case {case_id} after {duration:.2f}s: {str(e)}"
        # Ensure error is reported via queue if possible
        try:
            # Send structured error message
            q.put({"message": error_message, "percentage": 100, "error": True})
        except Exception as q_err:
            log_message(f"Failed to put error message in queue for case {case_id}: {q_err}", level="ERROR")

        log_message(error_message, level="CRITICAL")
        import traceback
        log_message(traceback.format_exc(), level="ERROR") # Log full traceback

    finally:
        # Signal completion (this might be redundant if WorkflowManager/CaseProcessor does it)
        # Ensure "DONE" is sent ONLY ONCE. If CaseProcessor sends it via report_progress(..., 100), remove this.
        # For safety, let's assume it might not always be sent, so we send it here.
        # Consider adding a flag or checking queue status before sending.
        try:
            # Check if DONE was already sent (e.g., by checking the last item)
            # This is complex with multiprocessing queues. A simpler approach is needed.
            # For now, we risk sending DONE twice, but ensure it's sent.
             q.put("DONE") # Ensure frontend knows processing is finished
        except Exception as q_done_err:
             log_message(f"Failed to put DONE message in queue for case {case_id}: {q_done_err}", level="ERROR")


        # Manage queue
        try:
            # Remove the processed case from the front of the queue
            if case_update_queue and case_update_queue[0]['case_id'] == case_id:
                case_update_queue.pop(0)
                log_message(f"Removed case {case_id} from update queue.", level="INFO")
            else:
                # This case might have been removed or queue modified unexpectedly
                log_message(f"Warning: Case {case_id} not found at the front of the queue after processing.", level="WARNING")
                # Attempt to find and remove it anyway, if necessary
                case_update_queue = [c for c in case_update_queue if c['case_id'] != case_id]


            # Clean up the queue reference (optional, prevents memory leak if updates are frequent)
            # if case_id in update_progress_queues:
            #     del update_progress_queues[case_id]
            #     log_message(f"Cleaned up progress queue reference for case {case_id}.", level="DEBUG")

        except Exception as final_err:
            log_message(f"Error during queue cleanup for case {case_id}: {final_err}", level="ERROR")

        # Process the next case if any
        case_processing_active = False # Mark processing as inactive *before* starting next
        process_next_case_in_queue()

def init_visualizer_routes(app):
    """Initialize the visualizer API routes"""
    app.add_url_rule('/api/cases', 'get_cases_with_filters', get_cases_with_filters, methods=['GET'])
    app.add_url_rule('/api/cases/validation', 'update_validation_status', update_validation_status, methods=['POST'])
    app.add_url_rule('/api/cases/update', 'update_existing_case', update_existing_case, methods=['POST'])
    app.add_url_rule('/api/cases/update/status/<case_id>', 'stream_update_status', stream_update_status, methods=['GET'])
    app.add_url_rule('/api/cases/<case_id>/steps', 'get_case_steps', get_case_steps, methods=['GET'])
    app.add_url_rule('/api/cases/improve-plaintiff', 'improve_single_plaintiff_name', improve_single_plaintiff_name, methods=['POST'])

    return app

def get_case_steps(case_id):
    """API endpoint to get steps for a specific case"""
    from DatabaseManagement.Connections import get_gz_connection
    import pandas as pd

    try:
        case_id = int(case_id)

        with get_gz_connection() as gz_connection:
            # Get only the necessary columns
            df_steps = pd.read_sql_query(
                f"SELECT step_nb, step_date_filed, proceeding_text, proceeding_text_cn, files_downloaded, files_failed, update_time "
                f"FROM tb_case_steps WHERE case_id = {case_id}",
                gz_connection
            )

            # Replace NaN values with None (will become null in JSON)
            df_steps = df_steps.replace({np.nan: None})

            # Convert step_nb to numeric for proper sorting
            df_steps['step_nb_numeric'] = pd.to_numeric(df_steps['step_nb'], errors='coerce')

            # Sort by the numeric version of step_nb
            df_steps = df_steps.sort_values('step_nb_numeric').drop('step_nb_numeric', axis=1)

            # Convert to list of dictionaries for JSON response
            steps = df_steps.to_dict(orient='records')

            return jsonify({
                'success': True,
                'case_id': case_id,
                'steps': steps
            })
    except Exception as e:
        log_message(f"Error getting steps for case {case_id}: {e}", level="ERROR", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def improve_single_plaintiff_name():
    """API endpoint to improve plaintiff name for a single case"""
    data = request.get_json()
    if not data or 'case_id' not in data:
        return jsonify({'error': 'Missing case_id parameter'}), 400

    try:
        case_id_int = int(data['case_id'])
    except ValueError:
        return jsonify({'error': 'Invalid case ID format'}), 400

    try:
        # Call the imported processing function
        # Pass the main cached dataframes - assume the function modifies them or handles updates
        result = process_plaintiff_improvement(case_id_int, cache_manager.cached_cases_df,
            cache_manager.cached_plaintiff_df, cache_manager.cached_plaintiff_reviews)

        # Refresh cache to reflect potential changes made by the process function
        if not cache_manager.refresh_cached_data(force=True):
            log_message(f"Warning: Cache refresh failed after plaintiff improvement for case {case_id_int}", level="WARNING")

        # Fetch the latest review status from the *refreshed* cache
        updated_review = {}
        if cache_manager.cached_plaintiff_reviews is not None and not cache_manager.cached_plaintiff_reviews.empty:
             review_row = cache_manager.cached_plaintiff_reviews[cache_manager.cached_plaintiff_reviews['case_id'] == case_id_int]
             if not review_row.empty:
                 updated_review = review_row.iloc[0].to_dict()


        return jsonify({
            'success': True,
            'message': result.get('message', 'Plaintiff name improvement process initiated/completed'), # Use message from result if available
            'proposed_name': updated_review.get('proposed_name'), # Get updated value from cache
            'method_info': updated_review.get('method_info'), # Get updated value from cache
            'status': updated_review.get('status', 'unknown') # Return current status from cache
        })
    except Exception as e:
        log_message(f"Error calling improve_single_plaintiff_name API for case {case_id_int}: {e}", level="CRITICAL")
        # Attempt to refresh cache even on error
        try: cache_manager.refresh_cached_data(force=True)
        except: pass
        return jsonify({'success': False, 'error': f'An unexpected server error occurred: {str(e)}'}), 500
