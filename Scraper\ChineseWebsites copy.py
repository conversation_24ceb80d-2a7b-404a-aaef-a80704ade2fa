import asyncio, re, json, aiohttp, os, sys, mimetypes, hashlib, shutil
from PIL import Image # Pillow imports (ExifTags not needed)
import piexif # Added piexif import for EXIF manipulation
sys.path.append(os.getcwd())
from bs4 import BeautifulSoup
from markdownify import markdownify
from AI.GC_VertexAI import vertex_genai_multi_async, vertex_genai_image_gen_async
from AI.GCV_GetImageParts import get_image_parts_async
from AI.LLM_shared import get_json, get_json_list
from Check.Do_Check_Download import download_from_url # Keep existing correct import
from langfuse.decorators import observe, langfuse_context
from Common.LangfuseClient import langfuse_client
import Common.Constants as Constants

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# --- Website Configuration ---
WEBSITE_CONFIG = [
    {
        "name": "10100",
        "search_url_template": "https://www.10100.com/search/xxxx",
        "exclude_words": [], # Add specific words if needed for this site during URL finding
        "watermark_description": "blue and white diagonal retangle watermark", # To be used later if needed
        "image_download_pattern": r"/p/", # Pattern for images to download from this site
        "content_selector": None # None that can be identifies
        
    },
    {
        "name": "SellerDefense",
        "search_url_template": "https://sellerdefense.cn/?s=xxxx",
        "exclude_words": ["allcase"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "yellow diagonal SellerDefense watermark",
        "image_download_pattern": None, # No specific pattern for this site
        "content_selector": ".content"
    },
    {
        "name": "Maijiazhichi",
        "search_url_template": "https://maijiazhichi.com/?s=xxxx",
        "exclude_words": ["tro"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "grey-blue diagonal watermark",
        "image_download_pattern": None, # No specific pattern for this site
        "content_selector": ".entry-main"
    },
    # Add more site configurations as needed
]
# --------------------------

async def fetch_url(session: aiohttp.ClientSession, url: str) -> str | None:
    """Fetches content from a given URL."""
    try:
        async with session.get(url, timeout=40) as response:
            response.raise_for_status() # Raise an exception for bad status codes
            return await response.text()
    except aiohttp.ClientError as e:
        print(f"Error fetching {url}: {e}")
        return None
    except asyncio.TimeoutError:
        print(f"Timeout fetching {url}")
        return None

def format_case_number(case_number: str) -> list[str]:
    """
    Generates different formats for the case number.
    Input: e.g., "1:25-cv-00097"
    Output: ["25-cv-00097", "25-cv-97", "25cv97"]
    """
    match = re.match(r'\d+:(\d+)-([a-zA-Z]+)-(\d+)', case_number)
    if not match:
        # Handle cases where the input format might be different or invalid
        # For now, return empty list or raise error? Let's return empty for robustness.
        print(f"Warning: Could not parse case number format: {case_number}")
        return []

    part1 = match.group(1)
    part2 = match.group(2)
    part3 = match.group(3)
    part3_short = str(int(part3)) # Remove leading zeros

    formats = [
        f"{part1}-{part2}-{part3}",
        f"{part1}-{part2}-{part3_short}",
        f"{part1}{part2}{part3_short}"
    ]
    return formats



# Step A: find the URL of the page
@observe(capture_input=False, capture_output=False)
async def A_find_target_urls_using_markdown(docket: str, plaintiff_name: str, session: aiohttp.ClientSession, search_url_template: str, case_formats: list[str], exclude_words: list[str]) -> list[str]:
    """Searches case formats on a website, converts to Markdown, and extracts target article URLs via regex."""
    target_urls = set() # Use a set to avoid duplicates

    # Determine base URL for resolving relative links found in markdown
    base_domain_match = re.match(r'(https?://[^/]+)', search_url_template)
    base_url = base_domain_match.group(1) if base_domain_match else None

    for case_format in case_formats:
        search_url = search_url_template.replace("xxxx", case_format)
        print(f"Searching (Markdown method): {search_url}")
        content = await fetch_url(session, search_url)
        if not content:
            continue

        # Convert HTML to Markdown
        try:
            # Use body_width=0 to prevent line wrapping that might break URLs
            markdown_content = markdownify(content, heading_style="ATX", bullets='*', body_width=0)
        except Exception as e:
            print(f"Error converting HTML to Markdown for {search_url}: {e}")
            continue

        prompt = f'{markdown_content} \n\n' + f'This page shows search results for {docket}. I am looking for information about a legal case (case number {docket} from plaintiff "{plaintiff_name}"). What are the relevant result for getting information about the case? For each result (hopefully just one), I need its title and its URL. Return your answer as a JSON array of objects, where each object has a "title" key and a "url" key. For example: [{{"title": "Result Title", "url": "/url/to/result"}}]. If there are no relevant results, you return and empty list []'
        response = await vertex_genai_multi_async([("text", prompt)], model_name=Constants.SMART_MODEL_FREE)
        json_list_response = get_json_list(response)
        for json_response in json_list_response:
            # Check if the URL contains any of the exclude words
            url = json_response.get("url")
            if any(word in url for word in exclude_words):
                continue
            else:
                url = base_url + url if url.startswith('/') else url
                target_urls.add(url)

    print(target_urls)
    return list(target_urls)

# Step B: extract the Reg Numbers and Copyright images from the page
@observe(capture_input=False, capture_output=False)
async def B_extract_data_from_url(session: aiohttp.ClientSession, url: str, site_name: str, docket: str, plaintiff_name: str, date_filed: object) -> tuple[dict | None, dict]:
    """
    Fetches a target URL, converts to Markdown, processes images (downloading based on config),
    sends to LLM, and returns extracted data along with source info and a map of downloaded images.

    Args:
        session: The aiohttp client session.
        url: The URL of the page to process.
        site_name: The name of the website (used to get config).
        docket: The case docket number.
        date_filed: The date the case was filed (used for constructing download paths).

    Returns:
        A tuple: (json_response, downloaded_images_map)
        - json_response: The dictionary parsed from the LLM response, or None on error.
        - downloaded_images_map: A dictionary mapping {temp_persistent_path: original_image_url} for images downloaded.
    """
    downloaded_images_map = {} # {temp_persistent_path: original_image_url}
    print(f"Fetching data from: {url} (Site: {site_name})")
    content = await fetch_url(session, url)
    if not content:
        return None, downloaded_images_map

    soup = BeautifulSoup(content, 'html.parser')

    # Retrieve config for the current site
    site_config = next((config for config in WEBSITE_CONFIG if config["name"] == site_name), None)
    content_selector = None
    image_download_pattern = None
    if site_config:
        content_selector = site_config.get("content_selector")
        image_download_pattern = site_config.get("image_download_pattern")

    # Determine the HTML content to convert to Markdown
    html_to_convert = ""
    if content_selector:
        selected_element = soup.select_one(content_selector)
        if selected_element:
            html_to_convert = str(selected_element)
        else:
            print(f"Warning: Content selector '{content_selector}' not found for {url}. Falling back to body.")
            html_to_convert = str(soup.body) if soup.body else str(soup)
    else:
        html_to_convert = str(soup.body) if soup.body else str(soup) # Fallback to whole body if no selector

    # Convert the relevant HTML part to Markdown
    markdown_content = markdownify(html_to_convert, heading_style="ATX", bullets='*', body_width=0)

    # --- Image Processing and Input List Construction ---
    prompt_input_list = []
    # Define persistent download directory
    case_folder = Constants.sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket}")
    copyright_cn_allpictures_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "copyright_cn_allpictures")
    os.makedirs(copyright_cn_allpictures_dir, exist_ok=True)

    last_end = 0
    image_index = 0
    # Regex to find Markdown images: ![alt text](url)
    for match in re.finditer(r'!\[.*?\]\((.*?)\)', markdown_content):
        # Append text chunk before the image
        text_chunk = markdown_content[last_end:match.start()]
        if text_chunk: prompt_input_list.append(("text", text_chunk))

        full_image_url = match.group(1)
        
        # If (pattern not set) or (pattern is set & found)
        if (not image_download_pattern or re.search(image_download_pattern, full_image_url)) and "data:image/" not in full_image_url:
            original_filename = full_image_url.split("?")[0].split("/")[-1] # Get the last part of the URL before any query string
            temp_persistent_path = os.path.join(copyright_cn_allpictures_dir, original_filename)
            await download_from_url(full_image_url, temp_persistent_path)

            # If download succeeded (or file already existed), add to prompt and map
            if os.path.exists(temp_persistent_path):
                image_index += 1
                prompt_input_list.append(("text", f"\n[Provided Image Identifier: Image_{image_index}]\n"))
                prompt_input_list.append(("image_path", temp_persistent_path))
                downloaded_images_map[f"Image_{image_index}"] = (temp_persistent_path, full_image_url) # Still useful for metadata

        last_end = match.end()

    # Append the final text chunk
    final_text_chunk = markdown_content[last_end:]
    if final_text_chunk: prompt_input_list.append(("text", final_text_chunk))

    # Filter out empty text items
    prompt_input_list = [item for item in prompt_input_list if item[0] != "text" or item[1].strip()]

    # --- End Image Processing ---

    # Prepare the LLM prompt (Instruction part)
    prompt = f"""
Analyze the following content, which consists of text and images, extracted from the webpage {url}.
Identify and extract all intellectual property registration numbers mentioned related to legal case {docket} filed by plaintiff {plaintiff_name}.
Specifically look for:
1.  Trademark Registration Numbers (usually start with digits or contain specific keywords like "Trademark Reg. No.")
2.  Patent Registration Numbers (often start with "US" followed by digits, or contain keywords like "Patent No.")
3.  Copyright Registration Numbers (often start with "VA", "TX", "SR", or contain keywords like "Copyright Reg. No.")

For copyright you also provide the image identifier of the image that appear to be related to Copyright registrations. Try to associate each copyright image with a specific Copyright Registration Number if possible. If an image is present but no clear registration number is nearby, list the image URL with a placeholder key like "no_reg_X". If a registration number is found with no associated image, list it with a null value for the image URL.
Additionally, check the page (especially near copyright information) for a single URL pointing to the artist's main website or portfolio (artist_url). If found, include it as a top-level key in the JSON.

Base your analysis *only* on the text provided and the content of the images. Do not infer or invent information (such as registration numbers) that is not explicitly present in the text or visible in the images.

Return the extracted information ONLY as a JSON object with the following structure:
{{
  "trademarks": ["list of strings"],
  "patents": ["list of strings"],
  "copyrights": [
    {{ "reg_no": "VAx123456", "identifier": "image_identifier_a"}},
    {{ "reg_no": "VAx234567", "identifier": null}},
    {{ "reg_no": "no_reg_1", "identifier": "image_identifier_b"}},
    {{ "reg_no": "multi", "identifier": "image_identifier_c"}}
  ],
  "artist_url": "url_string_or_null"
}}
where
- "copyrights": A list of JSON objects. Each object should have a "reg_no" key (string, for the registration number, or a placeholder like "no_reg_1", or "multi" if the image has multiple copyrighted images in a single image) and an "identifier" key (string, for the image identifier found in the markdown, or null if no image is associated).
- "artist_url": Contains the single artist website URL found on the page, or null if none was found.

Do not include any introductory text, explanations, or markdown formatting in your response. Just the JSON object.
"""
    # Construct the final input for the LLM
    llm_input = [("text", prompt)] + prompt_input_list

    # Make the LLM call
    response = await vertex_genai_multi_async(llm_input, model_name="gemini-2.5-flash-preview-04-17", useVertexAI=False) # gemini-2.5-pro-exp-03-25

    # Process LLM response
    try:
        json_response = get_json(response)
        # Add source information to the result
        if isinstance(json_response, dict):
            json_response["source_page_url"] = url
            json_response["source_site"] = site_name
        return json_response, downloaded_images_map # Return map along with response
    except json.JSONDecodeError:
        print(f"Error decoding LLM response for {url}: {response}")
        return None, downloaded_images_map # Still return map even if LLM fails
    except Exception as e:
        print(f"Unexpected error processing LLM response for {url}: {e}")
        return None, downloaded_images_map # Still return map


# Step C: process the copyrighted images
@observe(capture_input=False, capture_output=False)
async def C_copyright_process_picture(reg_no, img_url, case_folder, source_page_url, source_site, watermark_description, download_path: str | None = None):
    """Downloads original, adds metadata to it, removes watermark using GenAI, saves clean version."""
    # Define directories
    watermarked_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder, "copyright_cn_selected_watermarked")
    final_dir = os.path.join(os.getcwd(), "Documents", "IP", "Copyrights", case_folder) # Directory for watermark-removed files
    os.makedirs(watermarked_dir, exist_ok=True) # Ensure watermarked directory exists
    os.makedirs(final_dir, exist_ok=True) # Ensure final directory exists

    # Define paths
    try:
        # Guess extension or use a default if split fails
        url_parts = img_url.split(".")
        file_extension = url_parts[-1] if len(url_parts) > 1 else "jpg" # Default extension
    except Exception:
        file_extension = "jpg" # Fallback extension


    try:
        # Determine the final path for the original image (used for metadata and GenAI input)
        watermark_path = os.path.join(watermarked_dir, f"{Constants.sanitize_name(reg_no)}_original.{file_extension}")
        # Copy the file to the final original name (direct operation)
        shutil.copy(download_path, watermark_path)
        print(f"Copied download {download_path} -> {watermark_path}")
        
        # 1. Check image format and convert if necessary before adding metadata (note that the images_parts are alway in jpg))
        try:
            with Image.open(watermark_path) as img:
                img_format = img.format.upper() # Get format (JPEG, PNG, etc.)
                if img_format not in ['JPEG', 'TIFF']:
                    print(f"Original format {img_format} not supported by piexif. Converting {reg_no} to JPEG.")
                    # Ensure image is in RGB mode for JPEG saving
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Create new path for JPEG version
                    base, _ = os.path.splitext(watermark_path)
                    new_jpeg_path = f"{base}.jpg"
                    
                    # Save as JPEG
                    img.save(new_jpeg_path, "JPEG")
                    print(f"Saved converted JPEG to: {new_jpeg_path}")
                    
                    # Remove original non-JPEG/TIFF file
                    try:
                        os.remove(watermark_path)
                        print(f"Removed original file: {watermark_path}")
                    except OSError as rm_err:
                        print(f"Warning: Could not remove original file {watermark_path} after conversion: {rm_err}")
                        
                    # Update path to point to the new JPEG file
                    watermark_path = new_jpeg_path
                    
        except FileNotFoundError:
             print(f"Error: Downloaded file not found at {watermark_path} before format check.")
             raise # Re-raise the error to be caught by the outer try-except
        except Exception as img_err:
            print(f"Error during image format check/conversion for {reg_no}: {img_err}")
            # Decide how to handle: skip metadata? raise error? For now, skip metadata attempt.
            raise # Re-raise to be caught by outer try-except

        # 2. Add Metadata to Original File (now guaranteed to be JPEG/TIFF or skipped)
        metadata = {
            "source_site": source_site,
            "source_page_url": source_page_url,
            "original_image_url": img_url,
            "processing_step": "original_download" # Indicate this is the original
        }
        metadata_json = json.dumps(metadata)
        try:
            # Load original image's EXIF, add comment, insert back into file
            exif_dict = piexif.load(watermark_path)
            comment_bytes = metadata_json.encode('utf-8')
            exif_dict["Exif"][piexif.ExifIFD.UserComment] = comment_bytes
            exif_bytes = piexif.dump(exif_dict)
            piexif.insert(exif_bytes, watermark_path) # Insert EXIF into the original file
            print(f"Added metadata to original file: {watermark_path}")
        except Exception as meta_err:
            # If EXIF loading/insertion fails (e.g., non-JPEG), we skip adding metadata but continue
            print(f"Warning: Could not add metadata to original file {watermark_path}: {meta_err}")
            
        if reg_no.startswith("multi"):
            print(f"Using get_image_parts_async for {reg_no}")
            images_parts_with_label = await get_image_parts_async("Individual images that might be copyrighted", watermark_path)
            # images_parts_with_label is a list of json: [{"label": parts_details[i]["label"], "path": part_path, "width": part.shape[1], "height": part.shape[0]}]
            images_to_search = [image_json["path"] for image_json in images_parts_with_label]
        else:
            images_to_search = [watermark_path]


        # 3. Reverse search on google for images_to_search
        for image_path in images_to_search:
            # image_filenames_json, metadata_list_json = search_and_save_images(image_path, all_images_folder_path, num_results=10)
            pass
        
        
        if reg_no.startswith("multi"):
            return [{"reg": reg_no, "image_path": final_image_path} for final_image_path in images_to_search]
        

        # 4. If not found in Google reverse search : Watermark Removal using GenAI (using the original file path)
        if not reg_no.startswith("multi"): # if it is multiple picture, it is not possible to use LLM to remove the watermark
            # Dynamically create the watermark removal prompt
            watermark_removal_prompt = f"Your job is to generate a picture exactly the same but remove the {watermark_description}"
            print(f"Using watermark removal prompt for {reg_no}: '{watermark_removal_prompt}'")
                    
            final_image_path = None # Path of the watermark-removed image
            inline_data = await vertex_genai_image_gen_async(
                [("text", watermark_removal_prompt), ("image_path", watermark_path)] # Pass the original path
            )

            if inline_data and not isinstance(inline_data, str): # Check if we got image data
                processed_image_bytes = inline_data.data # Assign bytes here
                mime_type = inline_data.mime_type
                genai_extension = mimetypes.guess_extension(mime_type) or f".{mime_type.split('/')[-1]}" # Guess extension

                # Define final path for the watermark-removed image
                final_image_path_base = os.path.join(final_dir, f"{Constants.sanitize_name(reg_no)}_genai") # Save in the main case folder
                final_image_path = f"{final_image_path_base}{genai_extension}"

                # 4. Save GenAI Result (WITHOUT metadata)
                try:
                    with open(final_image_path, "wb") as f:
                        f.write(processed_image_bytes)
                    print(f"Saved watermark-removed image for {reg_no} to {final_image_path}")
                    return [{"reg": reg_no, "image_path": final_image_path}] # Return the final path (or None)
                
                except Exception as save_err:
                    print(f"Error saving watermark-removed image for {reg_no}: {save_err}")
                    return [] # Indicate save failure

            else:
                print(f"GenAI did not return image data for {reg_no}. Skipping watermark removal save.")
                return [] # No watermark-removed image generated
        else:
            final_image_path = images_to_search

    except Exception as e:
        print(f"Error during download/metadata/genai process for {reg_no}: {e}")
        # Clean up potentially incomplete original download if it exists and an error occurred
        if 'original_local_path' in locals() and os.path.exists(watermark_path) and final_image_path is None: # Only delete if processing failed
             try:
                 os.remove(watermark_path)
                 print(f"Removed incomplete original file: {watermark_path}")
             except OSError as rm_err:
                 print(f"Error removing incomplete original file {watermark_path}: {rm_err}")

        return []
        

@observe(capture_input=False, capture_output=False)
async def scrape_case_data(date_filed: str, docket: str, plaintiff_name:str, required_ip: list[str]) -> dict:
    """
    Main function to scrape data for a given case number from multiple websites.
    """
    case_formats = format_case_number(docket)
    if not case_formats:
        return {"error": f"Invalid case number format: {docket}"}

    all_trademarks = set()
    all_patents = set()
    # Store { reg: { "img_url": url, "source_page_url": page_url, "source_site": site_name, "local_path": path } }
    # Initialize local_path later after download/genai
    all_copyrights_image_paths = []
    all_artist_urls = set() # To store unique artist URLs found across pages

    async with aiohttp.ClientSession() as session:
        # Create tasks to extract data from each unique target URL found
        target_url_tasks_with_site = []
        for site_config in WEBSITE_CONFIG:
            target_url_tasks_with_site.append(A_find_target_urls_using_markdown(docket, plaintiff_name, session, site_config["search_url_template"], case_formats, site_config["exclude_words"])) # Pass exclude_words if needed
        url_results_per_site = await asyncio.gather(*target_url_tasks_with_site)

        # Create data extraction tasks, passing the site name
        extraction_tasks = []
        processed_urls = set() # Keep track of URLs already scheduled for extraction
        for i, site_config in enumerate(WEBSITE_CONFIG):
            site_name = site_config["name"]
            urls_from_this_site = url_results_per_site[i]
            for url in urls_from_this_site:
                if url not in processed_urls:
                    extraction_tasks.append(B_extract_data_from_url(session, url, site_name, docket, plaintiff_name, date_filed)) # Pass date_filed
                    processed_urls.add(url)

        print(f"Scheduled {len(extraction_tasks)} unique URLs for data extraction.")

        # Run all data extraction tasks concurrently
        results = await asyncio.gather(*extraction_tasks)

        # Now process the aggregated JSON results
        multi_index = 1
        no_reg_index = 1
        processed_copyright_results_all = []
        for data, downloaded_map in results:
            if data == None or not isinstance(data, dict): 
                continue
            
            # data is the json_response dict
            source_page_url = data.get("source_page_url", "Unknown") # Get source page URL
            source_site = data.get("source_site", "Unknown") # Get source site name

            if data.get("trademarks") and "trademark" in required_ip:
                all_trademarks.update(data["trademarks"])
                
            if data.get("patents") and "patent" in required_ip:
                all_patents.update(data["patents"])
                
            if data.get("copyrights") and isinstance(data["copyrights"], list) and "copyright" in required_ip:
                process_copyrights_pictures_tasks = []
                case_folder = Constants.sanitize_name(f"{date_filed.strftime('%Y-%m-%d')} - {docket}")
                # if multiple item.get("reg_no") have the same item.get("identifier") => merge and make the item.get("reg_no") = "multi"

                for item in data["copyrights"]:
                    reg_no = item.get("reg_no")
                    img_identifier = item.get("identifier")

                    if not reg_no: # Skip if reg_no is missing
                        print(f"Warning: Skipping copyright item due to missing 'reg_no': {item}")
                        continue

                    # Ensure img_identifier is a string or None
                    if not (isinstance(img_identifier, str) or img_identifier is None):
                        print(f"Warning: Skipping invalid copyright image identifier format for reg_no '{reg_no}': {img_identifier}")
                        continue

                    img_path, img_url = None, None
                    if img_identifier and img_identifier in downloaded_map:
                        img_path, img_url  = downloaded_map[img_identifier]
                    elif img_identifier:
                        print(f"Warning: Image identifier '{img_identifier}' for reg_no '{reg_no}' not found in downloaded_map.")
                        # img_url will remain None, img_path will remain None
                        
                    if img_path not in all_copyrights_image_paths:
                        if reg_no.startswith("multi"):
                            reg_no = f"multi_{multi_index}"
                            multi_index += 1
                        elif reg_no.startswith("no_reg_"):
                            reg_no = f"no_reg_{no_reg_index}"
                            no_reg_index += 1
                        all_copyrights_image_paths.append(img_path)
                        process_copyrights_pictures_tasks.append(
                            C_copyright_process_picture(
                                reg_no=reg_no,
                                img_url=img_url,
                                case_folder=case_folder,
                                source_page_url=source_page_url,
                                source_site=source_site,
                                watermark_description=next((site["watermark_description"] for site in WEBSITE_CONFIG if site["name"] == source_site), "the watermark"),
                                download_path=img_path
                            )
                        )
                        
                processed_results = await asyncio.gather(*process_copyrights_pictures_tasks)
                # flatten the list of list
                processed_copyright_results_all.extend([processed_result for sublist in processed_results for processed_result in sublist])

        # Prepare final output, only include local_path if it exists
        final_copyrights = {
            processed_result["reg"]: processed_result["image_path"] # Return the path to the final processed image
            for processed_result in processed_copyright_results_all
            if processed_result["image_path"] # Only include if final processing succeeded
        }
                        
        # Aggregate Artist URL
        # artist_url = data.get("artist_url")
        # if artist_url and isinstance(artist_url, str):
        #     all_artist_urls.add(artist_url)

        
    return {
        "trademarks": sorted(list(all_trademarks)),
        "patents": sorted(list(all_patents)),
        "copyrights": final_copyrights, # Use the final dict with local paths
        "artist_urls": sorted(list(all_artist_urls)) # Added artist URLs list
    }


# Example Usage (can be run with `python -m Scraper.scraper`)
if __name__ == "__main__":
    async def main():
        import pandas as pd
        from DatabaseManagement.ImportExport import get_table_from_GZ
        date = pd.to_datetime("2025-03-14")
        # test_case_docket = "1:25-cv-02710"
        test_case_docket = "1:25-cv-03655"
        # test_case_docket = "1:25-cv-00097"

        plaintiff_name = None # Initialize plaintiff_name

        case_where_clause = f"docket = '{test_case_docket}'"
        # Using force_refresh=False to align with previous behavior for these lookups.
        df_case = get_table_from_GZ("tb_case", force_refresh=False, where_clause=case_where_clause)

        if not df_case.empty:
            plaintiff_id = df_case["plaintiff_id"].values[0]
            
            plaintiff_where_clause = f"id = {plaintiff_id}" # id is numeric as confirmed
            df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=False, where_clause=plaintiff_where_clause)
            
            if not df_plaintiff.empty:
                plaintiff_name = df_plaintiff["plaintiff_name"].values[0]
            else:
                print(f"Plaintiff with ID {plaintiff_id} not found for case {test_case_docket}.")
                # plaintiff_name remains None
        else:
            print(f"Case with docket {test_case_docket} not found.")
            # plaintiff_name remains None

        if plaintiff_name: # Proceed only if plaintiff_name was found
            print(f"Scraping data for case: {test_case_docket} (Plaintiff: {plaintiff_name})")
            data = await scrape_case_data(date, test_case_docket, plaintiff_name, ["patent", "trademark", "copyright"])
            print("\n--- Final Result ---")
            print(json.dumps(data, indent=2))
        else:
            print(f"Could not retrieve plaintiff name for case {test_case_docket}. Skipping scrape.")

    asyncio.run(main())
