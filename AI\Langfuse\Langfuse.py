from langfuse import Langfuse
import pandas as pd
import os
import json

# Play with API: https://api.reference.langfuse.com/#tag/comments

# Documentation: https://python.reference.langfuse.com/langfuse/client#Langfuse.get_generations
# https://langfuse.com/guides/cookbook/example_query_data_via_sdk


def get_langfuse_model_name(model_name):
    if model_name == "gemini-2.0-flash-exp":
        return "gemini-2.0-flash-sdc"
    elif model_name == "gemini-2.5-pro-exp-03-25":
        return "gemini-2.0-pro-sdc"
    elif model_name == "gemini-2.5-pro-exp-03-25":
        return "gemini-2.0-flash-thinking-exp-sdc"
    else:
        return model_name

def get_langfuse_client():
    langfuse = Langfuse(
        secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
        public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
        host=os.getenv("LANGFUSE_HOST")  
    )
    return langfuse


# helper function
def pydantic_list_to_dataframe(pydantic_list):
    """
    Convert a list of pydantic objects to a pandas dataframe.
    """
    data = []
    for item in pydantic_list:
        data.append(item.dict())
    return pd.DataFrame(data)



def fetch_all_generations():
    langfuse = get_langfuse_client()
    all_generations = []
    page = 1
    limit = 100 
    

    while True:
        observations = langfuse.fetch_observations(type='GENERATION', page=page, limit=limit)
        all_generations.extend(observations.data)
        if len(observations.data) < limit:
            break
        
        page += 1

    df = pydantic_list_to_dataframe(all_generations)
    
    return df

def fetch_all_traces():
    langfuse = get_langfuse_client()
    all_traces = []
    page = 1
    limit = 100 
    

    while True:
        traces = langfuse.fetch_traces(page=page, limit=limit)
        all_traces.extend(traces.data)
        if len(traces.data) < limit:
            break
        
        page += 1

    return all_traces

def create_trace_dataframe():
    langfuse = get_langfuse_client()
    traces = fetch_all_traces()
    trace_data = []

    for trace in traces:
        # for observation in trace.observations:
        #     obs = langfuse.fetch_observation(observation)
        #     obs_output = obs.data.output
        
        if trace.output is None:
            continue

        output_json = json.loads(trace.output)

        if "results" not in output_json:
            continue

        results = output_json["results"]
    

        trace_data.append({
            "trace_id": trace.id,
            "session_id": trace.session_id,
            "timestamp": trace.timestamp,
            "total_time_taken": trace.latency,
            "total_cost": trace.total_cost,
            "number_of_observations": len(trace.observations),
            "final_output": trace.output,
            "bookmarked": trace.bookmarked,
            "public": trace.public,
            "risk_level": output_json["risk_level"],
            "nb_results": len(results),
            "result_1_type" : results[0]["type"] if len(results) > 0 else "",
            "result_1_ip_owner" : results[0]["ip_owner"] if len(results) > 0 else "",
            "result_1_report" : results[0]["report"] if len(results) > 0 and results[0]["type"] != "trademark" else "",
            "result_1_risk_description" : results[0]["risk_description"] if len(results) > 0 else "",
            "result_1_ip_image" : results[0]["ip_image"] if len(results) > 0 else "",
            "result_2_type" : results[1]["type"] if len(results) > 1 else "",
            "result_2_ip_owner" : results[1]["ip_owner"] if len(results) > 1 else "",
            "result_2_report" : results[1]["report"] if len(results) > 1 and results[1]["type"] != "trademark" else "",
            "result_2_risk_description" : results[1]["risk_description"] if len(results) > 1 else "",
            "result_2_ip_image" : results[1]["ip_image"] if len(results) > 1 else "",
            "result_3_type" : results[2]["type"] if len(results) > 2 else "",
            "result_3_ip_owner" : results[2]["ip_owner"] if len(results) > 2 else "",
            "result_3_report" : results[2]["report"] if len(results) > 2 and results[2]["type"] != "trademark" else "",
            "result_3_risk_description" : results[2]["risk_description"] if len(results) > 2 else "",
            "result_3_ip_image" : results[2]["ip_image"] if len(results) > 2 else ""
        })
    df = pd.DataFrame(trace_data)
    return df


if __name__ == "__main__":
    print(os.getenv("LANGFUSE_HOST"))

    df = create_trace_dataframe()
    print(df)
    df.to_csv("trace_data.csv", index=False)
    

    # Use the function to get all generations
    # df = fetch_all_generations()
    # df.drop(columns=['id', 'traceId', 'type', "projectId", "endTime", "createdAt", "updatedAt", "level", "promptTokens", "completionTokens", "totalTokens"], inplace=True, errors='ignore')

    # df['startTime'] = pd.to_datetime(df['startTime']).dt.strftime('%Y-%m-%d %H:%M:%S')
    # df.dropna(axis=1, how='all', inplace=True)
    # print(df)