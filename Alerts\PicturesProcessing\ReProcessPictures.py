import os, sys, datetime, time, traceback, json, base64, pytesseract, pandas, zlib, shutil
sys.path.append(os.getcwd())
import pandas as pd
from logdata import log_message
from Common.Constants import local_case_folder, nas_case_folder, sanitize_name
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
# from FileManagement.SendToNAS import sftp_exists, transfer_nas_to_local, transfer_local_to_nas, sftp_remove_directory, send_files_to_nas
from FileManagement.NAS import NASConnection
from FileManagement.Tencent_COS import send_pictures_to_cos
from Alerts.PicturesProcessing.ProcessPictures import get_template
from Alerts.CaseProcessor import process_case
from Alerts.PicturesProcessing.ProcessPicturesShared import is_compressed
from concurrent.futures import ThreadPoolExecutor, as_completed


os.environ['OMP_THREAD_LIMIT'] = '1'  
# OMP_THREAD_LIMIT=1 setting only affects OpenMP parallelization, which is used internally by Tesseract OCR. It is critical for Tesseract to run at ok speed in a container. It won't affect:
# 1. Python's threading (threading module)
# 2. Python's multiprocessing (multiprocessing module)
# 3. Flask's thread handling
# 4. Your start_task endpoint or any other threading in your application

if os.name == "nt":
    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
else:
    pytesseract.pytesseract.tesseract_cmd = 'tesseract'


def reprocess_case(df_db, df, plaintiff_df, update_prod=False):
    try:
        copyright_template1_hash, copyright_template1_size = get_template(os.path.join(os.getcwd(), 'data', 'ImageTemplates', 'Copyright1'))
        log_message("Connected to NAS successfully.")
        with NASConnection() as nas, ThreadPoolExecutor(max_workers=2) as nas_pool, ThreadPoolExecutor(max_workers=2) as cos_pool, ThreadPoolExecutor(max_workers=2) as db_pool:
            download_futures = {}
            upload_futures = []
            for i, (index, row) in enumerate(df.iterrows()):
                # for item in os.listdir(local_case_folder):
                #     item_path = os.path.join(local_case_folder, item)
                #     if os.path.isdir(item_path):
                #         stat_info = os.stat(item_path)
                #         creation_time = stat_info.st_ctime
                #         current_time = time.time()
                #         if (current_time - creation_time) > 3600:  # 3600 seconds = 60 minutes
                #             shutil.rmtree(item_path)
                                # print(f" 🔥🔥🔥 Deleting folder: {item_path}")
                # os.makedirs(local_case_folder, exist_ok=True)  # Recreate the directory
                
                case_name = sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}")
                case_directory_nas = f"{nas_case_folder}/{case_name}"
                case_directory_local = os.path.join(local_case_folder, case_name)                

                print(f'\n\n⚠️⚠️⚠️ {i+1}/{len(df)}: PDFs for {case_name}, Loc in DF = {index}')

                ## Check if folder exists on NAS and copy it over (if it is not already in local)
                # For first case only (i=0)
                if i == 0 and not os.path.exists(case_directory_local) and nas.ssh_exists(case_directory_nas):
                    print(f'⚠️ {i+1}/{len(df)}: PDFs for {case_name} -> Downloading from NAS')
                    nas.ssh_nas_to_local(case_directory_nas, case_directory_local, "*.pdf")
                elif i == 0 and os.path.exists(case_directory_local):
                    print(f'⚠️ {i+1}/{len(df)}: PDFs for {case_name} -> Local folder already exists')

                # Async download for next case while processing current
                if i+1 < len(df):
                    next_row = df.iloc[i+1]
                    next_case_name = sanitize_name(f"{pd.to_datetime(next_row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {next_row['docket']}")
                    next_case_dir_nas = f"{nas_case_folder}/{next_case_name}"
                    next_case_dir_local = os.path.join(local_case_folder, next_case_name)
                    if not os.path.exists(next_case_dir_local) and nas.ssh_exists(next_case_dir_nas):
                        log_message(f'⚠️ {i+2}/{len(df)}: PDFs for {next_case_name} -> Downloading from NAS')
                        download_futures[i+1] = nas_pool.submit(nas.ssh_nas_to_local, next_case_dir_nas, next_case_dir_local, "*.pdf")
                    elif os.path.exists(next_case_dir_local):
                        log_message(f'⚠️ {i+2}/{len(df)}: PDFs for {next_case_name} -> Local folder already exists')

                # Wait for current case's download if it was async started
                if i in download_futures:
                    start_time = time.time()
                    download_futures[i].result()
                    end_time = time.time()
                    print(f"Time taken to download the case: {end_time - start_time:.1f} seconds. This should be 0 if the async was done correctly")


                # df_single = df.loc[[index]]
                if os.path.exists(case_directory_local):
                    # Delete images folder and PDF folders
                    case_images_directory = os.path.join(case_directory_local, 'images')
                    if os.path.exists(case_images_directory):
                        shutil.rmtree(case_images_directory)
                    
                    case_directory_1 = os.path.join(case_directory_local, '1')
                    if os.path.exists(case_directory_1):
                        for item in os.listdir(case_directory_1):
                            if os.path.isdir(os.path.join(case_directory_1, item)):
                                shutil.rmtree(os.path.join(case_directory_1, item))
                    
                    # Process single row
                    # df = process_pictures(df_db, df, plaintiff_df, index, copyright_template1_hash, copyright_template1_size, force=True)
                    
                    if update_prod:                            
                        # Submit async I/O operations
                        # Remove the directory first if it exists.
                        if nas.ssh_exists(case_directory_nas):
                            nas.ssh_remove_directory(case_directory_nas)
                        
                        # Now run the three I/O operations concurrently.
                        df_single_copy =  df.loc[[index]].copy() # With the images as string. Which is what insert_and_update_df_to_GZ_batch expects
                        upload_futures.append(nas_pool.submit(nas.send_files_to_nas, df_single_copy, plaintiff_df))
                        upload_futures.append(cos_pool.submit(send_pictures_to_cos, df_single_copy))
                        upload_futures.append(db_pool.submit(insert_and_update_df_to_GZ_batch, df_single_copy, "tb_case", "id"))
                        
                        # df.loc[index] = df_single.iloc[0]
                    
                    print(f"Successfully reprocessed case {case_name}")
                else:
                    df.at[index, "images"] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
                    df_single_copy = df.loc[[index]].copy()
                    log_message(f"❌ {i+1}/{len(df)} Case folder not found on NAS (or no PDF in the folder): {case_name}", level='WARNING')
                    if update_prod:
                        upload_futures.append(db_pool.submit(insert_and_update_df_to_GZ_batch, df_single_copy, "tb_case", "id"))

            for future in as_completed(upload_futures):
                future.result()

        log_message("All cases reprocessed successfully")

    except Exception as e:
        error_msg = traceback.format_exc()
        log_message(f'Error in reprocess_case: {error_msg}', level='ERROR')
        raise

    return df


def print_all_patent_info(df):
    product_names = []
    problems = []
    
    for index, row in df.iterrows():
        if "patents" in row['images']:
            for patent_image in row['images']['patents'].keys():
                if "patent_number" in row['images']['patents'][patent_image]:
                    if row['images']['patents'][patent_image]['patent_number'] == "":
                        # df.at[index, 'images']['patents'][patent_image]['patent_number'] = "Not Found"
                        print(f"empty patent number: {patent_image}")
                    else:
                        # print(f"Existing Patent number: {row['images']['patents'][patent_image]['patent_number']}")
                        pass
                else:
                    # df.at[index, 'images']['patents'][patent_image]['patent_number'] = "Not Found"
                    print(f"missing patent number: {patent_image}")

                if "product_name" in row['images']['patents'][patent_image]:
                    if row['images']['patents'][patent_image]['product_name'] == "":
                        print(f"empty product name: {patent_image}")
                    elif "\n" in row['images']['patents'][patent_image]['product_name']:
                        print(f"\033[91mproduct name contains newline: {patent_image}\033[0m")
                        df.at[index, 'images']['patents'][patent_image]['product_name'] = row['images']['patents'][patent_image]['product_name'].replace("\n", " ")

                    else:
                        # print(f"Existing Product name: {row['images']['patents'][patent_image]['product_name']}")
                        pass
                        product_names.append(row['images']['patents'][patent_image]['product_name'])
                        if len(row['images']['patents'][patent_image]['product_name']) < 5 or any(char.isdigit() for char in row['images']['patents'][patent_image]['product_name']) or not row['images']['patents'][patent_image]['product_name'].isupper():
                            if row['images']['patents'][patent_image]['product_name'] != "BRA":
                                print(f"product name is not valid: {row['images']['patents'][patent_image]['product_name']} =>  {patent_image}")
                                problems.append(index)
                else:
                    print(f"missing product name: {patent_image}")
                    problems.append(index)

    product_names = list(set(product_names))
    problems = list(set(problems))
    print("")
    print("Unique product names:")
    print(product_names)
    # insert_and_update_df_to_GZ_batch(df, "tb_case", "id")
    return problems


def get_all_copyright_cases(df):
    copyright_cases = []
    
    for index, row in df.iterrows():
        if "copyrights" in row['images']:
            if len(row['images']['copyrights'].keys()) > 0:
                copyright_cases.append(index)

    copyright_cases = list(set(copyright_cases))
    return copyright_cases


def create_copyright_database(df, plaintiff_df):
    copyright_data = []

    for index, row in df.iterrows():
        if "copyrights" in row['images'] and row['images']['copyrights']:
            plaintiff_id = row['plaintiff_id']
            # Find plaintiff name, handle cases where plaintiff_id might not be in plaintiff_df
            plaintiff_name_row = plaintiff_df[plaintiff_df['id'] == plaintiff_id]['plaintiff_name']
            plaintiff_name = plaintiff_name_row.iloc[0] if not plaintiff_name_row.empty else "Unknown Plaintiff"

            for copyright_filename, copyright_info in row['images']['copyrights'].items():
                copyright_full_filename = copyright_info['full_filename'][0]  # full_filename is a list
                reg_no = copyright_info['reg_no']
                link = f"https://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{copyright_filename}"
                full_link = f"https://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{copyright_full_filename}"

                copyright_data.append({
                    'plaintiff_name': plaintiff_name,
                    'plaintiff_id': plaintiff_id,
                    'copyright_filename': copyright_filename,
                    'link': link,
                    'copyright_full_filename': copyright_full_filename,
                    'full_link': full_link,
                    'reg_no': reg_no,
                    'date_filed': row['date_filed'], 
                    'docket': row['docket']
                })


    copyright_df = pd.DataFrame(copyright_data)
    copyright_df.to_csv('copyright_database.csv', index=False)
    return copyright_df

def print_images_info(df):
    for index, row in df.iterrows():
        print(row['images'])



def fix_brand_in_trademark_text(cases_df):
    modified_cases = []
    for i, row in cases_df.iterrows():
        images = row["images"]
        if isinstance(images, str):
            images = json.loads(images)
        elif isinstance(images, dict):
            images = row["images"]

        
        needs_update = False
        if images:
            for trademark_image in images['trademarks']:
                if 'product_name' in images['trademarks'][trademark_image]:
                    print(f"\033[91mProduct name in trademark field: {row['docket']}\033[0m")
                    images['patents'] = images['trademarks']
                    images['trademarks'] = {}
                    needs_update = True
                    break
                elif 'trademark_text' not in images['trademarks'][trademark_image]:
                    print(f"\033[91mNo trademark text found: {row['docket']}\033[0m")

        if needs_update:
            cases_df.at[i, 'images'] = images
            modified_cases.append(row['id'])  # Assuming 'id' is the primary key


    brand_list = list(set(brand_list))
    if modified_cases:
        modified_df = cases_df[cases_df['id'].isin(modified_cases)]
        insert_and_update_df_to_GZ_batch(modified_df, "tb_case", "id")
        print(f"Updated {len(modified_cases)} cases with patent fixes")



if __name__ == "__main__":
    df = get_table_from_GZ("tb_case", force_refresh=True)
    # Ensure images is a dictionary, but don't try to decompress it
    df["images"] = df["images"].apply(lambda x: x if x else {"trademarks": {}, "patents": {}, "copyrights": {}})
    plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)
    # print_images_info(df)
    df_db = df.copy()

    # By Case number
    # df = df[ (df['docket'].str.contains('1:24-cv-02884')) | (df['docket'].str.contains('10780')) ]
    # df = df[ (df['docket'].str.contains('1:24-cv-02884'))]
    
    # By Time updated
    # df = df[df['update_time'] <= '2025-01-10 01:00:00']
    # copyright_cases = get_all_copyright_cases(df)
    # df = df.loc[copyright_cases]
    
    # import datetime
    # df = df[df['date_filed'] == datetime.date(2024, 4, 23)]

    # Sort the dataset
    # df = df.sort_values(by='update_time', ascending=False)
    # df = df.sort_values(by='date_filed', ascending=False)


    # Get cases without images
    df_cases_without_images = df[(df["images"] == {"trademarks": {}, "patents": {}, "copyrights": {}}) & (df["cause"].notna())]
    # print(f"Cases without images: {len(df_cases_without_images)}")

    # Get cases that already have images for trademarks, copyrights and patents
    # df_with_trademark_and_copyright = df[df["images"].apply(lambda x: (x.get('trademarks', {})) != {} and (x.get('copyrights', {})) != {})]
    # df_with_trademark_images = df[df["images"].apply(lambda x: (x.get('trademarks', {})) != {})]
    df_with_patent_images = df[df["images"].apply(lambda x: (x.get('patents', {})) != {})]
    print(f"Cases with patent images: {len(df_with_patent_images)}")

    # Get cases that are trademarks cases
    # df_trademark_cases = df[df["nos_description"].apply(lambda x: "Trademark" in str(x) if pd.notna(x) else False)]
    # df_copyright_cases = df[df["nos_description"].apply(lambda x: "Copyrights" in str(x) if pd.notna(x) else False)]
    # df_patent_cases = df[df["nos_description"].apply(lambda x: "Patent" in str(x) if pd.notna(x) else False)]

    # Get cases that are trademark but have no images
    # df_trademark_cases_without_images = df_cases_without_images[df_cases_without_images["nos_description"].str.contains("Trademark")]
    # df_copyright_cases_without_images = df_cases_without_images[df_cases_without_images["nos_description"].str.contains("Copyrights")]
    df_patent_cases_without_images = df_cases_without_images[df_cases_without_images["nos_description"].str.contains("Patent")]
    print(f"Patent cases without images: {len(df_patent_cases_without_images)}")

    df_to_process = pd.concat([df_patent_cases_without_images, df_with_patent_images])
    df_to_process = df_to_process.drop_duplicates(subset=['docket', 'date_filed'])
    df_to_process = df_to_process[df_to_process['update_time'] < datetime.datetime(2025, 3, 26, 10, 0, 0)]
    df_to_process = df_to_process.sort_values(by='date_filed', ascending=False)
    print(f"Cases to process: {len(df_to_process)}")
    
    
    print(f"Processing {len(df_to_process)} cases with patents")
    reprocess_case(df_db, df_to_process, plaintiff_df, update_prod=True)  


    

