print("Loading RAG.py")
import os
import numpy as np
from PIL import Image
from tf_keras import applications
from tf_keras import preprocessing
from tf_keras import mixed_precision
from sentence_transformers import SentenceTransformer
import time
from AI.GC_Credentials import get_gcs_credentials

# Path to the image directory
model_clipv2 = None
model_efficientnet = None
structured_patent_image_array = None 
structured_patent_text_array = None 
structured_copyright_array = None
structured_copyright_array_clipv2 = None

def load_all_models():
    global model_clipv2
    if model_clipv2 is None:
        print(f"\nLoading SentenceTransformer model jinaai/jina-clip-v2 in RAG.py")

        # Define the custom cache folder for Hugging Face models
        huggingface_cache_dir = os.path.join(os.getcwd(), "data", "Models", "huggingface")

        # Create the Hugging Face cache directory if it doesn't exist
        os.makedirs(huggingface_cache_dir, exist_ok=True)

        try:
            # Attempt to load the model with the custom cache folder
            model_clipv2 = SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=huggingface_cache_dir)
        except AttributeError:
            print("Encountered AttributeError, attempting to clear cache and reload.")
            # Clear the cache for the specific model
            SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=None)
            # Re-attempt to load the model with the custom cache folder
            model_clipv2 = SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=huggingface_cache_dir)

    global model_efficientnet
    if model_efficientnet is None:
        print("Loading EfficientNet (Keras) model in RAG.py")

        # Float16 yielded bad results when tried on EfficientNetV2L
        # mixed_precision.set_global_policy('mixed_float16')

        # Define the custom cache folder for Keras models
        keras_cache_dir = os.path.join(os.getcwd(), "data", "Models", "keras")

        # Create the Keras cache directory if it doesn't exist
        os.makedirs(keras_cache_dir, exist_ok=True)

        # Set the environment variable for Keras cache
        # os.environ['KERAS_HOME'] = keras_cache_dir

        # Load the EfficientNetB7 model (it will now use the custom cache location)
        model_efficientnet = applications.EfficientNetB7(weights='imagenet', include_top=False, pooling='avg')
        # model_efficientnet2 = applications.EfficientNetV2L(weights='imagenet', include_top=False, pooling='avg', input_shape=(480, 480, 3))

    try:
        global structured_patent_text_array
        structured_patent_text_array = load_embeddings_npy('EmbeddingsPatentTexts.npy')
    except Exception as e:
        print(f"Error loading EmbeddingsPatentTexts.npy: {e}")

    try:
        global structured_patent_image_array
        structured_patent_image_array = load_embeddings_npy('EmbeddingsPatentImages.npy')
    except Exception as e:
        print(f"Error loading EmbeddingsPatentImages.npy: {e}")

    try:
        global structured_copyright_array
        structured_copyright_array = load_embeddings_npy('EmbeddingsCopyright.npy')
    except Exception as e:
        print(f"Error loading EmbeddingsCopyright.npy: {e}")

    try:
        global structured_copyright_array_clipv2
        structured_copyright_array_clipv2 = load_embeddings_npy('EmbeddingsCopyrightClipV2.npy')
    except Exception as e:  
        print(f"Error loading EmbeddingsCopyrightClipV2.npy: {e}")


def load_embeddings_npy(embeddings_file):
   """Loads embeddings from a file or downloads them if the file does not exist."""
   file_path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', embeddings_file)
   if os.path.exists(file_path):
       structured_array = np.load(file_path, allow_pickle=True)
   else:
       import requests
       url = f'http://www.dinsightsgroup.com/{embeddings_file}'
       response = requests.get(url)
       with open(file_path, 'wb') as f:
           f.write(response.content)
       structured_array = np.load(file_path, allow_pickle=True)
   return structured_array


# def get_clipv2_embeddings(data_list, type):
#     if not model_clipv2:
#         load_all_models()
  
#     if type == "image":
#         # need to preprocess the image?
#         embeddings = model_clipv2.encode([Image.open(image) for image in data_list])
#     elif type =="text":
#         embeddings = model_clipv2.encode(data_list)
#     return embeddings


def get_clipv2_embeddings(data_list, type):
    if not model_clipv2:
        load_all_models()
  
    if type == "image":
        # Use context manager for image handling
        images = []
        embeddings = []
        for image_path in data_list:
            with Image.open(image_path) as img:
                images.append(img.copy())  # Copy image data before closing file
        embeddings = model_clipv2.encode(images)
        # Explicitly close images
        for img in images:
            img.close()
    elif type =="text":
        embeddings = model_clipv2.encode(data_list)
    return embeddings

# def preprocess_image(image_path, res=480):
#     """Preprocess the image to match EfficientNetB7 input requirements."""
#     img = Image.open(image_path).convert('RGB')
#     # img = img.resize((600, 600))  # Resize to EfficientNetB7 input size
#     img = img.resize((res, res))
#     x = preprocessing.image.img_to_array(img)
#     x = np.expand_dims(x, axis=0)  # Add batch dimension
#     x = applications.efficientnet.preprocess_input(x)  # Apply EfficientNet preprocessing
#     return x

def preprocess_image(image_path, res=480):
    """Preprocess the image to match EfficientNetB7 input requirements."""
    with Image.open(image_path).convert('RGB') as img:  # Context manager
        img = img.resize((res, res))
        x = preprocessing.image.img_to_array(img)
    x = np.expand_dims(x, axis=0)
    x = applications.efficientnet.preprocess_input(x)
    return x

# Batching image processing in tensor flow for speed
def get_efficientnet_embeddings(image_paths, res=480):
    """Generate embeddings for a batch of images."""
    # RAM: This lines consumes 4.2mb per image (600*600*3), 2.5mb per image (480*480*3)
    # time_start = time.time()
    preprocessed_images = [preprocess_image(image_path, res) for image_path in image_paths]
    # time_end = time.time()
    # print(f"Time taken to preprocess images: {time_end - time_start:.1f} seconds")
    # This line consumes the same amount again. Why?
    x = np.concatenate(preprocessed_images, axis=0)  # Combine into a batch

    del preprocessed_images
    if not model_efficientnet:
        load_all_models()
    # time_start = time.time()
    embeddings = model_efficientnet.predict(x)
    # time_end = time.time()
    # print(f"Time taken to get embeddings: {time_end - time_start:.1f} seconds")
    return embeddings


def get_efficientnet_embeddings2(image_paths):
    if not model_clipv2:
        load_all_models()
  
    embeddings = model_clipv2.encode([Image.open(image) for image in image_paths])
    return embeddings


def get_google_multimodal_embeddings(image_paths):
    import vertexai
    from vertexai.vision_models import Image, MultiModalEmbeddingModel
    
    # Initialize with service account credentials
    credentials = get_gcs_credentials()
    vertexai.init(
        project=os.environ.get("GOOGLE_CLOUD_PROJECT"),  # Hardcoded project ID from GC_VertexAI.py
        location=os.environ.get("GOOGLE_CLOUD_LOCATION", "us-central1"),
        credentials=credentials
    )

    model = MultiModalEmbeddingModel.from_pretrained("multimodalembedding@001")
    # image = Image.load_from_file("gs://cloud-samples-data/vertex-ai/llm/prompts/landmark1.png")
    image = Image.load_from_file(image_paths[0])


    embeddings = model.get_embeddings(
        image=image,
        # contextual_text="Colosseum",
        # dimension=1408,
    )

    # print(f"Image Embedding: {embeddings.image_embedding}")
    # print(f"Text Embedding: {embeddings.text_embedding}")

    return embeddings.image_embedding

    # Example response:
    # Image Embedding: [0.**********, -0.**********, 0.**********, ...]
    # Text Embedding: [0.********, -0.*********, 0.**********, ...]


# Function to find the most similar, trying to be universal, not used
# def find_most_similar(query_image_paths, top_n=1, similarity_threshold=0.8, structured_array=structured_copyright_array, get_embeddings_func=get_efficientnet_embeddings, get_embeddings_args={}):
#     """
#     Find the most similar image(s) to the query image in the embeddings database.
    
#     Args:
#         query_image_paths (list[str]): Paths to the query images.
#         top_n (int): Number of top similar images to return.
#         similarity_threshold (float): Minimum cosine similarity score to consider a match.
        
#     Returns:
#         List[Tuple[str, float]]: List of (image_id, similarity_score) for the top matches.
#     """

#     if len(query_image_paths) == 0:
#         return []
    
#     # Get embedding for the query image
#     query_embeddings = get_embeddings_func(query_image_paths, **get_embeddings_args)

#     # Ensure query_embeddings is always 2D
#     if query_embeddings.ndim == 1:
#         query_embeddings = query_embeddings.reshape(1, -1)

#     # Extract embeddings from the structured array
#     embeddings = np.array([entry['embedding'] for entry in structured_array])

#     # Compute cosine similarity between all query embeddings and all stored embeddings
#     similarities = cosine_similarity(query_embeddings, embeddings)

#     all_top_matches = []

#     for i, similarity_scores in enumerate(similarities):
#         # Sort results by similarity score in descending order
#         sorted_indices = np.argsort(similarity_scores)[::-1]
#         top_matches = []

#         for idx in sorted_indices[:top_n]:
#             if similarity_scores[idx] >= similarity_threshold:
#                 match_info = {
#                     'query_image_path': query_image_paths[i],
#                     'filename': structured_array[idx]['image_id'],
#                     'similarity': similarity_scores[idx],
#                     'plaintiff_name': structured_array[idx]['plaintiff_name'],
#                     'docket': structured_array[idx]['docket'],
#                     'number_of_cases': structured_array[idx]['number_of_cases']
#                 }
#                 top_matches.append(match_info)

#         all_top_matches.extend(top_matches)
    
#     return all_top_matches


def test_speed():
    # 50 images in 1 go is 28 seconds, 50 images one by one is 140 seconds
    
    folder_path = os.path.join(os.getcwd(), "data", "EvidencesJanFeb")
    image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
    image_paths = image_paths[:50]


    start_time = time.time()
    embeddings = get_efficientnet_embeddings(image_paths)
    end_time = time.time()
    print(f"Total time taken: {end_time - start_time:.1f} seconds")

    start_time = time.time()
    embeddings2 = []
    for image_path in image_paths[:50]:
        embeddings2.append(get_efficientnet_embeddings([image_path]))
    end_time = time.time()
    print(f"Total time taken one by one: {end_time - start_time:.1f} seconds")
    print(f"Embeddings shape: {embeddings.shape}")

def test_speed_clipv2():
    # 50 images in 1 go is 239 seconds, 50 images one by one is 211 seconds
    
    folder_path = os.path.join(os.getcwd(), "data", "EvidencesJanFeb")
    image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
    image_paths = image_paths[:50]

    start_time = time.time()
    print(f"Loading {len(image_paths)} images at once")
    embeddings = get_clipv2_embeddings(image_paths, "image")
    end_time = time.time()
    print(f"Total time taken: {end_time - start_time:.1f} seconds")

    start_time = time.time()
    embeddings2 = []
    print(f"Loading {len(image_paths)} images one by one")
    for image_path in image_paths[:50]:
        print(f"Loading image {image_path}")
        embeddings2.append(get_clipv2_embeddings([image_path], "image"))
    end_time = time.time()

    print(f"Total time taken one by one: {end_time - start_time:.1f} seconds")
    print(f"Embeddings shape: {embeddings.shape}")

get_clipv2_embeddings

if __name__ == "__main__":
    get_google_multimodal_embeddings(["D:/Win10User/Downloads/crop.jpg"])
    time_start = time.time()
    load_all_models()
    time_end = time.time()
    print(f"Time taken to load all models: {time_end - time_start:.1f} seconds")
    # test_speed()
    # test_speed_clipv2()
    # get_efficientnet_embeddings(["D:/Win10User/Downloads/crop.jpg"])